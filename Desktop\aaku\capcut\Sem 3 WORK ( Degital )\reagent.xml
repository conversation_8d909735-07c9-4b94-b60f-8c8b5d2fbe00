﻿<?xml version='1.0' encoding='utf-8'?>

<WindowsRE version="2.0">
  <WinreBCD id="{34c9205e-8ef0-11ec-af2c-00e04c68a5ef}"/>
  <WinreLocation path="\Recovery\WindowsRE" id="0" offset="************" guid="{1e385c63-798d-43b7-81ec-c3ddb09594fa}"/>
  <ImageLocation path="\Recovery\WindowsRE" id="0" offset="************" guid="{1e385c63-798d-43b7-81ec-c3ddb09594fa}"/>
  <PBRImageLocation path="" id="0" offset="0" guid="{00000000-0000-0000-0000-000000000000}" index="0"/>
  <PBRCustomImageLocation path="" id="0" offset="0" guid="{00000000-0000-0000-0000-000000000000}" index="0"/>
  <InstallState state="1"/>
  <OsInstallAvailable state="0"/>
  <CustomImageAvailable state="0"/>
  <IsAutoRepairOn state="1"/>
  <WinREStaged state="0"/>
  <OperationParam path=""/>
  <OperationPermanent state="0"/>
  <OsBuildVersion path="22000.1.amd64fre.co_release.210604-1628"/>
  <OemTool state="0"/>
  <IsServer state="0"/>
  <DownlevelWinreLocation path="" id="0" offset="0" guid="{00000000-0000-0000-0000-000000000000}"/>
  <IsWimBoot state="0"/>
  <NarratorScheduled state="0"/>
  <ScheduledOperation state="5"/>
</WindowsRE>