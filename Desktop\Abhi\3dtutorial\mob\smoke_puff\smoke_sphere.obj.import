[remap]

importer="wavefront_obj"
importer_version=1
type="Mesh"
uid="uid://bw1wcdadnrp8s"
path="res://.godot/imported/smoke_sphere.obj-7dec537f94ed12354ea65934deb2dc4e.mesh"

[deps]

files=["res://.godot/imported/smoke_sphere.obj-7dec537f94ed12354ea65934deb2dc4e.mesh"]

source_file="res://mob/smoke_puff/smoke_sphere.obj"
dest_files=["res://.godot/imported/smoke_sphere.obj-7dec537f94ed12354ea65934deb2dc4e.mesh", "res://.godot/imported/smoke_sphere.obj-7dec537f94ed12354ea65934deb2dc4e.mesh"]

[params]

generate_tangents=true
generate_lods=true
generate_shadow_mesh=true
generate_lightmap_uv2=false
generate_lightmap_uv2_texel_size=0.2
scale_mesh=Vector3(1, 1, 1)
offset_mesh=Vector3(0, 0, 0)
force_disable_mesh_compression=false
