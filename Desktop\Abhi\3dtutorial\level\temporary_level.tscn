[gd_scene load_steps=7 format=3 uid="uid://dp7sxe5xl5shk"]

[ext_resource type="Texture2D" uid="uid://buoj24gitja3m" path="res://level/checkboard.png" id="1_cig4d"]
[ext_resource type="PackedScene" uid="uid://44nmu6u7pt7g" path="res://player/Player.tscn" id="2_bc4qa"]

[sub_resource type="Environment" id="Environment_pev5d"]
background_mode = 1
background_color = Color(0, 0.807843, 1, 1)
ambient_light_source = 2
ambient_light_color = Color(0.815625, 0.702063, 0.54125, 1)
ambient_light_sky_contribution = 0.31
tonemap_mode = 2
tonemap_exposure = 1.05
tonemap_white = 0.95
ssao_intensity = 1.0
glow_normalized = true
glow_intensity = 0.2
fog_enabled = true
fog_light_color = Color(0, 0.478431, 0.764706, 1)
fog_density = 0.002
fog_height = 0.52
fog_height_density = 0.1319
volumetric_fog_density = 0.1525
volumetric_fog_albedo = Color(0.203922, 0.203922, 1, 1)
volumetric_fog_emission = Color(0.0431373, 0.0431373, 0.0431373, 1)

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_ooyhk"]
albedo_color = Color(1, 0.717647, 0.188235, 1)
albedo_texture = ExtResource("1_cig4d")
uv1_triplanar = true
uv1_triplanar_sharpness = 4.0

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_pixig"]
albedo_color = Color(0.286275, 0.682353, 0.427451, 1)
albedo_texture = ExtResource("1_cig4d")
uv1_triplanar = true
uv1_triplanar_sharpness = 4.0

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_7l0sc"]
albedo_color = Color(0.286275, 0.682353, 0.427451, 1)
albedo_texture = ExtResource("1_cig4d")
uv1_triplanar = true
uv1_triplanar_sharpness = 4.0

[node name="TemporaryLevel" type="Node3D"]

[node name="WorldEnvironment" type="WorldEnvironment" parent="."]
environment = SubResource("Environment_pev5d")

[node name="DirectionalLight3D" type="DirectionalLight3D" parent="."]
transform = Transform3D(0.707107, -0.612373, 0.353553, 0, 0.5, 0.866026, -0.707107, -0.612373, 0.353553, 0, 10, 0)
shadow_enabled = true
directional_shadow_max_distance = 40.0

[node name="Bridge2" type="CSGBox3D" parent="."]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 0, 10.5)
use_collision = true
size = Vector3(4, 1, 3)
material = SubResource("StandardMaterial3D_ooyhk")

[node name="Bridge5" type="CSGBox3D" parent="."]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 0, 14.5)
use_collision = true
size = Vector3(4, 1, 3)
material = SubResource("StandardMaterial3D_ooyhk")

[node name="Bridge6" type="CSGBox3D" parent="."]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 0, 18.5)
use_collision = true
size = Vector3(4, 1, 3)
material = SubResource("StandardMaterial3D_ooyhk")

[node name="Platform6" type="CSGBox3D" parent="."]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, -5, 0)
use_collision = true
size = Vector3(17, 11, 15)
material = SubResource("StandardMaterial3D_pixig")

[node name="Platform7" type="CSGBox3D" parent="."]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, -5, 26)
use_collision = true
size = Vector3(9, 11, 9)
material = SubResource("StandardMaterial3D_7l0sc")

[node name="CharacterBody3D" parent="." instance=ExtResource("2_bc4qa")]
transform = Transform3D(0.999956, 0, -0.00938969, 0, 1, 0, 0.00938969, 0, 0.999956, 0.0292909, 0.5, -0.00178057)
