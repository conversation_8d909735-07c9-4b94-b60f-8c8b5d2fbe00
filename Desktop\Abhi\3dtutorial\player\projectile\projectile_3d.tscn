[gd_scene load_steps=4 format=3 uid="uid://njbhsv307xvy"]

[ext_resource type="PackedScene" uid="uid://c2y6bst0e1buq" path="res://player/projectile/projectile_model.glb" id="1_ndq6i"]
[ext_resource type="Shader" uid="uid://b4wc5wsyakc0a" path="res://player/projectile/projectile.gdshader" id="2_chfaf"]

[sub_resource type="ShaderMaterial" id="ShaderMaterial_htc8w"]
render_priority = 0
shader = ExtResource("2_chfaf")
shader_parameter/tail_color = Color(0.0959041, 0.421525, 0.0244841, 1)
shader_parameter/head_color = Color(0.487762, 0.652543, 0.419427, 1)

[node name="projectile3D" instance=ExtResource("1_ndq6i")]

[node name="Projectile" parent="." index="0"]
material_override = SubResource("ShaderMaterial_htc8w")
