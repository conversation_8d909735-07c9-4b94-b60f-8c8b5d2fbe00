[gd_scene load_steps=4 format=3 uid="uid://da66udulvx7wk"]

[ext_resource type="Script" uid="uid://c2qsm075ak6or" path="res://mob/mob.gd" id="1_6jlfs"]
[ext_resource type="PackedScene" uid="uid://coxl003ari27j" path="res://mob/bat/bat_model.tscn" id="1_7b8ug"]

[sub_resource type="SphereShape3D" id="SphereShape3D_6jlfs"]
radius = 0.268876

[node name="mob" type="RigidBody3D"]
gravity_scale = 0.0
script = ExtResource("1_6jlfs")

[node name="CollisionShape3D" type="CollisionShape3D" parent="."]
shape = SubResource("SphereShape3D_6jlfs")

[node name="bat_model" parent="." instance=ExtResource("1_7b8ug")]
unique_name_in_owner = true

[node name="Timer" type="Timer" parent="."]
unique_name_in_owner = true
wait_time = 2.0
one_shot = true

[connection signal="timeout" from="Timer" to="." method="_on_timer_timeout"]
