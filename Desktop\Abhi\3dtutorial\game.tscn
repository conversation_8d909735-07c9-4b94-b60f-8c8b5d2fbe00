[gd_scene load_steps=9 format=3 uid="uid://behv0pnjdwiyh"]

[ext_resource type="Script" uid="uid://qlqxmk3sk28" path="res://mob/spawner/mob_spawner_3d.gd" id="1_7jktm"]
[ext_resource type="Texture2D" uid="uid://buoj24gitja3m" path="res://level/checkboard.png" id="1_e2o6t"]
[ext_resource type="PackedScene" uid="uid://44nmu6u7pt7g" path="res://player/Player.tscn" id="2_e2o6t"]
[ext_resource type="PackedScene" uid="uid://coxl003ari27j" path="res://mob/bat/bat_model.tscn" id="3_feb5d"]
[ext_resource type="PackedScene" uid="uid://c7bb2omma0o0x" path="res://mob/spawner/mob_spawner_3d.tscn" id="5_ryrav"]

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_feb5d"]
albedo_color = Color(0.497274, 0.189199, 0.70885, 1)
albedo_texture = ExtResource("1_e2o6t")
uv1_triplanar = true

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_fc0e3"]
albedo_color = Color(0.916751, 0.773097, 0.194639, 1)

[sub_resource type="Environment" id="Environment_feb5d"]

[node name="Game" type="Node3D"]
script = ExtResource("1_7jktm")

[node name="CSGBox3D" type="CSGBox3D" parent="."]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -1.05471, -2, -0.454739)
use_collision = true
size = Vector3(12.002, 4, 10)
material = SubResource("StandardMaterial3D_feb5d")

[node name="CSGBox3D2" type="CSGBox3D" parent="."]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 21.1571, -2, 12.2411)
use_collision = true
size = Vector3(10, 4, 35.3916)
material = SubResource("StandardMaterial3D_feb5d")

[node name="CSGBox3D3" type="CSGBox3D" parent="."]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -25.6581, -2, 2.98911)
use_collision = true
size = Vector3(17.7729, 4, 16.8877)
material = SubResource("StandardMaterial3D_feb5d")

[node name="CSGBox3D4" type="CSGBox3D" parent="."]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -12.8025, -1, 26.8478)
use_collision = true
size = Vector3(51.7329, 4, 10)
material = SubResource("StandardMaterial3D_feb5d")

[node name="CSGBox3D7" type="CSGBox3D" parent="."]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 7.94626, -0.5, -0.0547393)
material_override = SubResource("StandardMaterial3D_fc0e3")
use_collision = true
size = Vector3(5, 1, 2)

[node name="CSGBox3D13" type="CSGBox3D" parent="."]
transform = Transform3D(-1, 0, -8.74228e-08, 0, 1, 0, 8.74228e-08, 0, -1, -26.0537, -0.5, 13.9453)
material_override = SubResource("StandardMaterial3D_fc0e3")
use_collision = true
size = Vector3(5, 1, 2)

[node name="CSGBox3D9" type="CSGBox3D" parent="."]
transform = Transform3D(1, 0, 1.06581e-14, 0, 1, 0, -1.06581e-14, 0, 1, -12.0859, -0.922493, 0.12787)
material_override = SubResource("StandardMaterial3D_fc0e3")
use_collision = true
size = Vector3(10, 1, 2)

[node name="CSGBox3D10" type="CSGBox3D" parent="."]
transform = Transform3D(1, 0, 1.06581e-14, 0, 1, 0, -1.06581e-14, 0, 1, -1.0859, -0.922493, 9.12787)
material_override = SubResource("StandardMaterial3D_fc0e3")
use_collision = true
size = Vector3(10, 1, 2)

[node name="CSGBox3D11" type="CSGBox3D" parent="."]
transform = Transform3D(1, 0, 1.06581e-14, 0, 1, 0, -1.06581e-14, 0, 1, -1.0859, 1.07751, 18.1279)
material_override = SubResource("StandardMaterial3D_fc0e3")
use_collision = true
size = Vector3(10, 1, 2)

[node name="CSGBox3D14" type="CSGBox3D" parent="."]
transform = Transform3D(1, 0, 1.06581e-14, 0, 1, 0, -1.06581e-14, 0, 1, -26.0859, 1.07751, 18.1279)
material_override = SubResource("StandardMaterial3D_fc0e3")
use_collision = true
size = Vector3(10, 1, 2)

[node name="CSGBox3D12" type="CSGBox3D" parent="."]
transform = Transform3D(1, 0, 1.06581e-14, 0, 1, 0, -1.06581e-14, 0, 1, -1.0859, 0.077507, 14.1279)
material_override = SubResource("StandardMaterial3D_fc0e3")
use_collision = true
size = Vector3(10, 1, 2)

[node name="CSGBox3D8" type="CSGBox3D" parent="."]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, 12.1804, -0.5, -0.0547393)
material_override = SubResource("StandardMaterial3D_fc0e3")
use_collision = true
size = Vector3(5, 1, 2)

[node name="Player" parent="." instance=ExtResource("2_e2o6t")]
transform = Transform3D(1, 0, 1.06581e-14, 0, 1, 0, -1.06581e-14, 0, 1, 0.214168, 9.53674e-07, 0.117836)

[node name="DirectionalLight3D" type="DirectionalLight3D" parent="."]
transform = Transform3D(0.216506, 0.433013, 0.875, -0.75, -0.5, 0.433013, 0.625, -0.75, 0.216506, 0, 22, 14)
shadow_enabled = true

[node name="bat_model" parent="." instance=ExtResource("3_feb5d")]
transform = Transform3D(-4.37114e-08, 0, 1, 0, 1, 0, -1, 0, -4.37114e-08, -11, 2, 0)

[node name="WorldEnvironment" type="WorldEnvironment" parent="."]
environment = SubResource("Environment_feb5d")

[node name="MobSpawner3D" parent="." instance=ExtResource("5_ryrav")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -27, 1, 3)
