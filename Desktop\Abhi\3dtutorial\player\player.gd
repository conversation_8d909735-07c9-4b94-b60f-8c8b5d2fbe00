extends CharacterBody3D


func _ready():
	Input.set_mouse_mode(Input.MOUSE_MODE_CAPTURED)


func _unhandled_input(event):
	if event is InputEventMouseMotion:
		rotation_degrees.y -= event.relative.x * 0.2
		%Camera3D.rotation_degrees.x -= event.relative.y * 0.3
		%Camera3D.rotation_degrees.x = clamp(
			%Camera3D.rotation_degrees.x, -80.0,80.0
		)
	elif event.is_action_pressed("ui_cancel"):
		Input.set_mouse_mode(Input.MOUSE_MODE_VISIBLE)


func _physics_process(_delta):
	const SPEED = 4
	
	var input_direction_2D = Input.get_vector(
		"move backword","move forword","move left","move right"
	)
	var input_direction_3D = Vector3(
		input_direction_2D.x, 0.0 , input_direction_2D.y
	)		
	
	var direction = transform.basis * input_direction_3D
	
	velocity.x = direction.x * SPEED 
	velocity.z = direction.z * SPEED 
	
	velocity.y -= 20.0 * _delta
	if Input.is_action_just_pressed("Jump") and is_on_floor():
		velocity.y = 10.0
	elif Input.is_action_just_released("Jump") and velocity.y > 0.0:
		velocity.y = 0.0
	move_and_slide()
	
	if Input.is_action_pressed("shoot") and %Timer.is_stopped():
		shoot_bullet()
	
func shoot_bullet():
	const BULLET_3D = preload("res://player/bullet_3D.tscn")
	var new_bullet = BULLET_3D.instantiate()
	%Marker3D.add_child(new_bullet)
	
	new_bullet.global_transform = %Marker3D.global_transform 
	
	%Timer.start()
