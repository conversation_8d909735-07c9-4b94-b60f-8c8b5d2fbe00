import _plotly_utils.basevalidators


class HoverinfoValidator(_plotly_utils.basevalidators.EnumeratedValidator):
    def __init__(self, plotly_name="hoverinfo", parent_name="sankey.link", **kwargs):
        super(HoverinfoValidator, self).__init__(
            plotly_name=plotly_name,
            parent_name=parent_name,
            edit_type=kwargs.pop("edit_type", "calc"),
            values=kwargs.pop("values", ["all", "none", "skip"]),
            **kwargs,
        )
