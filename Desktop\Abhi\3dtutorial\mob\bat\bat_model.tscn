[gd_scene load_steps=13 format=3 uid="uid://coxl003ari27j"]

[ext_resource type="PackedScene" uid="uid://dwa0cufqq2ws1" path="res://mob/bat/bat_model.glb" id="1_1he7g"]
[ext_resource type="Material" uid="uid://bdfpj26aehorm" path="res://mob/bat/standard_material_3d.tres" id="2_030dj"]
[ext_resource type="Script" uid="uid://crtlmjuc8h26y" path="res://mob/bat/bat_model.gd" id="2_vgr6s"]

[sub_resource type="Animation" id="Animation_0gbdx"]
resource_name = "Idle"
length = 1.33333
loop_mode = 1
tracks/0/type = "position_3d"
tracks/0/imported = true
tracks/0/enabled = true
tracks/0/path = NodePath("Armature/Skeleton3D:body")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = PackedFloat32Array(0, 1, 0, -0.0074, 0, 0.0333333, 1, 0, -0.0142, 0, 0.0666667, 1, 0, -0.0198, 0, 0.1, 1, 0, -0.0236, 0, 0.166667, 1, 0, -0.0236, 0, 0.2, 1, 0, -0.0198, 0, 0.233333, 1, 0, -0.0142, 0, 0.266667, 1, 0, -0.0074, 0, 0.333333, 1, 0, 0.00739999, 0, 0.366667, 1, 0, 0.0142, 0, 0.4, 1, 0, 0.0198, 0, 0.433333, 1, 0, 0.0236, 0, 0.5, 1, 0, 0.0236, 0, 0.533333, 1, 0, 0.0198, 0, 0.566667, 1, 0, 0.0142, 0, 0.6, 1, 0, 0.00739999, 0, 0.666667, 1, 0, -0.0074, 0, 0.7, 1, 0, -0.0142, 0, 0.733333, 1, 0, -0.0198, 0, 0.766667, 1, 0, -0.0236, 0, 0.833333, 1, 0, -0.0236, 0, 0.866667, 1, 0, -0.0198, 0, 0.9, 1, 0, -0.0142, 0, 0.933333, 1, 0, -0.0074, 0, 1, 1, 0, 0.00739999, 0, 1.03333, 1, 0, 0.0142, 0, 1.06667, 1, 0, 0.0198, 0, 1.1, 1, 0, 0.0236, 0, 1.16667, 1, 0, 0.0236, 0, 1.2, 1, 0, 0.0198, 0, 1.23333, 1, 0, 0.0142, 0, 1.26667, 1, 0, 0.00739999, 0, 1.33333, 1, 0, -0.0074, 0)
tracks/1/type = "rotation_3d"
tracks/1/imported = true
tracks/1/enabled = true
tracks/1/path = NodePath("Armature/Skeleton3D:wing_1.L")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/keys = PackedFloat32Array(0, 1, -0.0286549, 0.188485, -0.686718, 0.701478, 0.0333333, 1, -0.0506094, 0.208724, -0.677509, 0.703459, 0.0666667, 1, -0.0610847, 0.215431, -0.666256, 0.71131, 0.1, 1, -0.05709, 0.207045, -0.655256, 0.724233, 0.133333, 1, -0.0396006, 0.186493, -0.644631, 0.74034, 0.166667, 1, -0.0107812, 0.157663, -0.633842, 0.757147, 0.2, 1, 0.0267386, 0.12419, -0.622616, 0.772147, 0.266667, 1, 0.114195, 0.0556545, -0.600903, 0.789163, 0.3, 1, 0.155982, 0.0253652, -0.59312, 0.789452, 0.333333, 1, 0.190651, 6.60251e-05, -0.590026, 0.784552, 0.366667, 1, 0.213966, -0.0185891, -0.593371, 0.775747, 0.4, 1, 0.222174, -0.0279345, -0.602828, 0.765805, 0.433333, 1, 0.213282, -0.0252487, -0.61726, 0.756877, 0.466667, 1, 0.19033, -0.0110549, -0.634785, 0.7488, 0.5, 1, 0.157487, 0.0130927, -0.653025, 0.740665, 0.533333, 1, 0.118744, 0.045142, -0.669545, 0.731827, 0.6, 1, 0.0378164, 0.121276, -0.689637, 0.712925, 0.633333, 1, 0.001578, 0.158023, -0.691096, 0.705275, 0.666667, 1, -0.0286549, 0.188485, -0.686718, 0.701478, 0.7, 1, -0.0506094, 0.208724, -0.677509, 0.703459, 0.733333, 1, -0.0610847, 0.215431, -0.666256, 0.71131, 0.766667, 1, -0.05709, 0.207045, -0.655256, 0.724233, 0.8, 1, -0.0396006, 0.186493, -0.644631, 0.74034, 0.833333, 1, -0.0107812, 0.157663, -0.633842, 0.757147, 0.866667, 1, 0.0267386, 0.12419, -0.622616, 0.772147, 0.933333, 1, 0.114195, 0.0556545, -0.600903, 0.789163, 0.966667, 1, 0.155982, 0.0253652, -0.59312, 0.789452, 1, 1, 0.190651, 6.60251e-05, -0.590026, 0.784552, 1.03333, 1, 0.213966, -0.0185891, -0.593371, 0.775747, 1.06667, 1, 0.222174, -0.0279345, -0.602828, 0.765805, 1.1, 1, 0.213282, -0.0252487, -0.61726, 0.756877, 1.13333, 1, 0.19033, -0.0110549, -0.634785, 0.7488, 1.16667, 1, 0.157487, 0.0130927, -0.653025, 0.740665, 1.2, 1, 0.118744, 0.045142, -0.669545, 0.731827, 1.26667, 1, 0.0378164, 0.121276, -0.689637, 0.712925, 1.3, 1, 0.001578, 0.158023, -0.691096, 0.705275, 1.33333, 1, -0.0286549, 0.188485, -0.686718, 0.701478)
tracks/2/type = "rotation_3d"
tracks/2/imported = true
tracks/2/enabled = true
tracks/2/path = NodePath("Armature/Skeleton3D:wing_2.L")
tracks/2/interp = 1
tracks/2/loop_wrap = true
tracks/2/keys = PackedFloat32Array(0, 1, -0.0560155, -0.016041, 0.274838, 0.959724, 0.0333333, 1, -0.107724, -0.0293087, 0.260889, 0.958892, 0.0666667, 1, -0.150109, -0.0400536, 0.254681, 0.954464, 0.1, 1, -0.178377, -0.0485314, 0.258005, 0.948293, 0.133333, 1, -0.188128, -0.0538743, 0.269983, 0.94277, 0.166667, 1, -0.176689, -0.0543572, 0.288976, 0.939318, 0.2, 1, -0.147367, -0.0491943, 0.312801, 0.937026, 0.233333, 1, -0.104947, -0.0380736, 0.338908, 0.934172, 0.266667, 1, -0.0542423, -0.0212793, 0.364585, 0.929345, 0.3, 1, 4.35922e-07, 2.55374e-07, 0.387197, 0.921997, 0.333333, 1, 0.0532716, 0.023607, 0.404457, 0.912699, 0.366667, 1, 0.101459, 0.0465808, 0.414628, 0.903117, 0.4, 1, 0.140874, 0.0655118, 0.416552, 0.895738, 0.433333, 1, 0.168002, 0.0771313, 0.410045, 0.893134, 0.466667, 1, 0.178911, 0.0792831, 0.397312, 0.896576, 0.5, 1, 0.170442, 0.0715782, 0.380524, 0.906106, 0.533333, 1, 0.14463, 0.056739, 0.360771, 0.919623, 0.566667, 1, 0.104948, 0.0380741, 0.338908, 0.934172, 0.6, 1, 0.0552696, 0.0184505, 0.316108, 0.946932, 0.633333, 1, 3.97739e-07, 2.93199e-07, 0.294044, 0.955792, 0.666667, 1, -0.0560155, -0.016041, 0.274838, 0.959724, 0.7, 1, -0.107724, -0.0293087, 0.260889, 0.958892, 0.733333, 1, -0.150109, -0.0400536, 0.254681, 0.954464, 0.766667, 1, -0.178377, -0.0485314, 0.258005, 0.948293, 0.8, 1, -0.188128, -0.0538743, 0.269983, 0.94277, 0.833333, 1, -0.176689, -0.0543572, 0.288976, 0.939318, 0.866667, 1, -0.147367, -0.0491943, 0.312801, 0.937026, 0.9, 1, -0.104947, -0.0380736, 0.338908, 0.934172, 0.933333, 1, -0.0542423, -0.0212793, 0.364585, 0.929345, 0.966667, 1, 4.35922e-07, 2.55374e-07, 0.387197, 0.921997, 1, 1, 0.0532716, 0.023607, 0.404457, 0.912699, 1.03333, 1, 0.101459, 0.0465808, 0.414628, 0.903117, 1.06667, 1, 0.140874, 0.0655118, 0.416552, 0.895738, 1.1, 1, 0.168002, 0.0771313, 0.410045, 0.893134, 1.13333, 1, 0.178911, 0.0792831, 0.397312, 0.896576, 1.16667, 1, 0.170442, 0.0715782, 0.380524, 0.906106, 1.2, 1, 0.14463, 0.056739, 0.360771, 0.919623, 1.23333, 1, 0.104948, 0.0380741, 0.338908, 0.934172, 1.26667, 1, 0.0552696, 0.0184505, 0.316108, 0.946932, 1.3, 1, 3.97739e-07, 2.93199e-07, 0.294044, 0.955792, 1.33333, 1, -0.0560155, -0.016041, 0.274838, 0.959723)
tracks/3/type = "rotation_3d"
tracks/3/imported = true
tracks/3/enabled = true
tracks/3/path = NodePath("Armature/Skeleton3D:wing_3.L")
tracks/3/interp = 1
tracks/3/loop_wrap = true
tracks/3/keys = PackedFloat32Array(0, 1, 0.0412136, 0.059985, 0.264753, 0.961566, 0.0333333, 1, -0.0159354, 0.0440908, 0.244883, 0.968419, 0.0666667, 1, -0.073461, 0.0305335, 0.228073, 0.970389, 0.1, 1, -0.126218, 0.0194948, 0.21651, 0.967891, 0.133333, 1, -0.169154, 0.0105836, 0.212432, 0.962366, 0.166667, 1, -0.197486, 0.00342183, 0.217359, 0.955899, 0.2, 1, -0.20688, -0.00128304, 0.230104, 0.950921, 0.233333, 1, -0.194684, -0.00193852, 0.248813, 0.948782, 0.266667, 1, -0.164213, 0.00231133, 0.271453, 0.948336, 0.3, 1, -0.120273, 0.011884, 0.29574, 0.947592, 0.333333, 1, -0.0677396, 0.0266105, 0.319228, 0.944879, 0.366667, 1, -0.0114933, 0.0454515, 0.339527, 0.939427, 0.4, 1, 0.0437561, 0.0664394, 0.354556, 0.931645, 0.433333, 1, 0.0936447, 0.086843, 0.362736, 0.923099, 0.466667, 1, 0.134205, 0.103508, 0.36302, 0.916238, 0.5, 1, 0.161688, 0.113444, 0.355316, 0.91364, 0.533333, 1, 0.172105, 0.114807, 0.34197, 0.916655, 0.566667, 1, 0.162369, 0.107434, 0.325273, 0.925361, 0.6, 1, 0.134745, 0.0938879, 0.306267, 0.937672, 0.633333, 1, 0.0929822, 0.077208, 0.285731, 0.950658, 0.666667, 1, 0.0412136, 0.059985, 0.264753, 0.961566, 0.7, 1, -0.0159354, 0.0440908, 0.244883, 0.968419, 0.733333, 1, -0.073461, 0.0305335, 0.228073, 0.970389, 0.766667, 1, -0.126218, 0.0194948, 0.21651, 0.967891, 0.8, 1, -0.169154, 0.0105836, 0.212432, 0.962366, 0.833333, 1, -0.197486, 0.00342183, 0.217359, 0.955899, 0.866667, 1, -0.20688, -0.00128304, 0.230104, 0.950921, 0.9, 1, -0.194684, -0.00193852, 0.248813, 0.948782, 0.933333, 1, -0.164213, 0.00231133, 0.271453, 0.948336, 0.966667, 1, -0.120273, 0.011884, 0.29574, 0.947592, 1, 1, -0.0677396, 0.0266105, 0.319228, 0.944879, 1.03333, 1, -0.0114933, 0.0454515, 0.339527, 0.939427, 1.06667, 1, 0.0437561, 0.0664394, 0.354556, 0.931645, 1.1, 1, 0.0936447, 0.086843, 0.362736, 0.923099, 1.13333, 1, 0.134205, 0.103508, 0.36302, 0.916238, 1.16667, 1, 0.161688, 0.113444, 0.355316, 0.91364, 1.2, 1, 0.172105, 0.114807, 0.34197, 0.916655, 1.23333, 1, 0.162369, 0.107434, 0.325273, 0.925361, 1.26667, 1, 0.134745, 0.0938879, 0.306267, 0.937672, 1.3, 1, 0.0929822, 0.077208, 0.285731, 0.950658, 1.33333, 1, 0.0412136, 0.059985, 0.264753, 0.961566)
tracks/4/type = "rotation_3d"
tracks/4/imported = true
tracks/4/enabled = true
tracks/4/path = NodePath("Armature/Skeleton3D:wing_1.R")
tracks/4/interp = 1
tracks/4/loop_wrap = true
tracks/4/keys = PackedFloat32Array(0, 1, -0.0286549, -0.188485, 0.686718, 0.701478, 0.0333333, 1, -0.0506094, -0.208724, 0.677509, 0.703459, 0.0666667, 1, -0.0610847, -0.215431, 0.666256, 0.71131, 0.1, 1, -0.05709, -0.207045, 0.655256, 0.724233, 0.133333, 1, -0.0396006, -0.186493, 0.644631, 0.74034, 0.166667, 1, -0.0107812, -0.157663, 0.633842, 0.757147, 0.2, 1, 0.0267386, -0.12419, 0.622616, 0.772147, 0.266667, 1, 0.114195, -0.0556545, 0.600903, 0.789163, 0.3, 1, 0.155982, -0.0253652, 0.59312, 0.789452, 0.333333, 1, 0.190651, -6.60251e-05, 0.590026, 0.784552, 0.366667, 1, 0.213966, 0.0185891, 0.593371, 0.775747, 0.4, 1, 0.222174, 0.0279345, 0.602828, 0.765805, 0.433333, 1, 0.213282, 0.0252487, 0.61726, 0.756877, 0.466667, 1, 0.19033, 0.0110549, 0.634785, 0.7488, 0.5, 1, 0.157487, -0.0130927, 0.653025, 0.740665, 0.533333, 1, 0.118744, -0.045142, 0.669545, 0.731827, 0.6, 1, 0.0378164, -0.121276, 0.689637, 0.712925, 0.633333, 1, 0.001578, -0.158023, 0.691096, 0.705275, 0.666667, 1, -0.0286549, -0.188485, 0.686718, 0.701478, 0.7, 1, -0.0506094, -0.208724, 0.677509, 0.703459, 0.733333, 1, -0.0610847, -0.215431, 0.666256, 0.71131, 0.766667, 1, -0.05709, -0.207045, 0.655256, 0.724233, 0.8, 1, -0.0396006, -0.186493, 0.644631, 0.74034, 0.833333, 1, -0.0107812, -0.157663, 0.633842, 0.757147, 0.866667, 1, 0.0267386, -0.12419, 0.622616, 0.772147, 0.933333, 1, 0.114195, -0.0556545, 0.600903, 0.789163, 0.966667, 1, 0.155982, -0.0253652, 0.59312, 0.789452, 1, 1, 0.190651, -6.60251e-05, 0.590026, 0.784552, 1.03333, 1, 0.213966, 0.0185891, 0.593371, 0.775747, 1.06667, 1, 0.222174, 0.0279345, 0.602828, 0.765805, 1.1, 1, 0.213282, 0.0252487, 0.61726, 0.756877, 1.13333, 1, 0.19033, 0.0110549, 0.634785, 0.7488, 1.16667, 1, 0.157487, -0.0130927, 0.653025, 0.740665, 1.2, 1, 0.118744, -0.045142, 0.669545, 0.731827, 1.26667, 1, 0.0378164, -0.121276, 0.689637, 0.712925, 1.3, 1, 0.001578, -0.158023, 0.691096, 0.705275, 1.33333, 1, -0.0286549, -0.188485, 0.686718, 0.701478)
tracks/5/type = "rotation_3d"
tracks/5/imported = true
tracks/5/enabled = true
tracks/5/path = NodePath("Armature/Skeleton3D:wing_2.R")
tracks/5/interp = 1
tracks/5/loop_wrap = true
tracks/5/keys = PackedFloat32Array(0, 1, -0.0560155, 0.016041, -0.274838, 0.959724, 0.0333333, 1, -0.107724, 0.0293087, -0.260889, 0.958892, 0.0666667, 1, -0.150109, 0.0400536, -0.254681, 0.954464, 0.1, 1, -0.178377, 0.0485314, -0.258005, 0.948293, 0.133333, 1, -0.188128, 0.0538743, -0.269983, 0.94277, 0.166667, 1, -0.176689, 0.0543572, -0.288976, 0.939318, 0.2, 1, -0.147367, 0.0491943, -0.312801, 0.937026, 0.233333, 1, -0.104947, 0.0380736, -0.338908, 0.934172, 0.266667, 1, -0.0542423, 0.0212793, -0.364585, 0.929345, 0.3, 1, 4.35922e-07, -2.55374e-07, -0.387197, 0.921997, 0.333333, 1, 0.0532716, -0.023607, -0.404457, 0.912699, 0.366667, 1, 0.101459, -0.0465808, -0.414628, 0.903117, 0.4, 1, 0.140874, -0.0655118, -0.416552, 0.895738, 0.433333, 1, 0.168002, -0.0771313, -0.410045, 0.893134, 0.466667, 1, 0.178911, -0.0792831, -0.397312, 0.896576, 0.5, 1, 0.170442, -0.0715782, -0.380524, 0.906106, 0.533333, 1, 0.14463, -0.056739, -0.360771, 0.919623, 0.566667, 1, 0.104948, -0.0380741, -0.338908, 0.934172, 0.6, 1, 0.0552696, -0.0184505, -0.316108, 0.946932, 0.633333, 1, 3.97739e-07, -2.93199e-07, -0.294044, 0.955792, 0.666667, 1, -0.0560155, 0.016041, -0.274838, 0.959724, 0.7, 1, -0.107724, 0.0293087, -0.260889, 0.958892, 0.733333, 1, -0.150109, 0.0400536, -0.254681, 0.954464, 0.766667, 1, -0.178377, 0.0485314, -0.258005, 0.948293, 0.8, 1, -0.188128, 0.0538743, -0.269983, 0.94277, 0.833333, 1, -0.176689, 0.0543572, -0.288976, 0.939318, 0.866667, 1, -0.147367, 0.0491943, -0.312801, 0.937026, 0.9, 1, -0.104947, 0.0380736, -0.338908, 0.934172, 0.933333, 1, -0.0542423, 0.0212793, -0.364585, 0.929345, 0.966667, 1, 4.35922e-07, -2.55374e-07, -0.387197, 0.921997, 1, 1, 0.0532716, -0.023607, -0.404457, 0.912699, 1.03333, 1, 0.101459, -0.0465808, -0.414628, 0.903117, 1.06667, 1, 0.140874, -0.0655118, -0.416552, 0.895738, 1.1, 1, 0.168002, -0.0771313, -0.410045, 0.893134, 1.13333, 1, 0.178911, -0.0792831, -0.397312, 0.896576, 1.16667, 1, 0.170442, -0.0715782, -0.380524, 0.906106, 1.2, 1, 0.14463, -0.056739, -0.360771, 0.919623, 1.23333, 1, 0.104948, -0.0380741, -0.338908, 0.934172, 1.26667, 1, 0.0552696, -0.0184505, -0.316108, 0.946932, 1.3, 1, 3.97739e-07, -2.93199e-07, -0.294044, 0.955792, 1.33333, 1, -0.0560155, 0.016041, -0.274838, 0.959723)
tracks/6/type = "rotation_3d"
tracks/6/imported = true
tracks/6/enabled = true
tracks/6/path = NodePath("Armature/Skeleton3D:wing_3.R")
tracks/6/interp = 1
tracks/6/loop_wrap = true
tracks/6/keys = PackedFloat32Array(0, 1, 0.0412136, -0.059985, -0.264753, 0.961566, 0.0333333, 1, -0.0159354, -0.0440908, -0.244883, 0.968419, 0.0666667, 1, -0.073461, -0.0305335, -0.228073, 0.970389, 0.1, 1, -0.126218, -0.0194948, -0.21651, 0.967891, 0.133333, 1, -0.169154, -0.0105836, -0.212432, 0.962366, 0.166667, 1, -0.197486, -0.00342183, -0.217359, 0.955899, 0.2, 1, -0.20688, 0.00128304, -0.230104, 0.950921, 0.233333, 1, -0.194684, 0.00193852, -0.248813, 0.948782, 0.266667, 1, -0.164213, -0.00231133, -0.271453, 0.948336, 0.3, 1, -0.120273, -0.011884, -0.29574, 0.947592, 0.333333, 1, -0.0677396, -0.0266105, -0.319228, 0.944879, 0.366667, 1, -0.0114933, -0.0454515, -0.339527, 0.939427, 0.4, 1, 0.0437561, -0.0664394, -0.354556, 0.931645, 0.433333, 1, 0.0936447, -0.086843, -0.362736, 0.923099, 0.466667, 1, 0.134205, -0.103508, -0.36302, 0.916238, 0.5, 1, 0.161688, -0.113444, -0.355316, 0.91364, 0.533333, 1, 0.172105, -0.114807, -0.34197, 0.916655, 0.566667, 1, 0.162369, -0.107434, -0.325273, 0.925361, 0.6, 1, 0.134745, -0.0938879, -0.306267, 0.937672, 0.633333, 1, 0.0929822, -0.077208, -0.285731, 0.950658, 0.666667, 1, 0.0412136, -0.059985, -0.264753, 0.961566, 0.7, 1, -0.0159354, -0.0440908, -0.244883, 0.968419, 0.733333, 1, -0.073461, -0.0305335, -0.228073, 0.970389, 0.766667, 1, -0.126218, -0.0194948, -0.21651, 0.967891, 0.8, 1, -0.169154, -0.0105836, -0.212432, 0.962366, 0.833333, 1, -0.197486, -0.00342183, -0.217359, 0.955899, 0.866667, 1, -0.20688, 0.00128304, -0.230104, 0.950921, 0.9, 1, -0.194684, 0.00193852, -0.248813, 0.948782, 0.933333, 1, -0.164213, -0.00231133, -0.271453, 0.948336, 0.966667, 1, -0.120273, -0.011884, -0.29574, 0.947592, 1, 1, -0.0677396, -0.0266105, -0.319228, 0.944879, 1.03333, 1, -0.0114933, -0.0454515, -0.339527, 0.939427, 1.06667, 1, 0.0437561, -0.0664394, -0.354556, 0.931645, 1.1, 1, 0.0936447, -0.086843, -0.362736, 0.923099, 1.13333, 1, 0.134205, -0.103508, -0.36302, 0.916238, 1.16667, 1, 0.161688, -0.113444, -0.355316, 0.91364, 1.2, 1, 0.172105, -0.114807, -0.34197, 0.916655, 1.23333, 1, 0.162369, -0.107434, -0.325273, 0.925361, 1.26667, 1, 0.134745, -0.0938879, -0.306267, 0.937672, 1.3, 1, 0.0929822, -0.077208, -0.285731, 0.950658, 1.33333, 1, 0.0412136, -0.059985, -0.264753, 0.961566)
tracks/7/type = "position_3d"
tracks/7/imported = true
tracks/7/enabled = true
tracks/7/path = NodePath("Armature/Skeleton3D:foot.L")
tracks/7/interp = 1
tracks/7/loop_wrap = true
tracks/7/keys = PackedFloat32Array(0, 1, 0.130992, -0.227392, -0.0878515)
tracks/8/type = "rotation_3d"
tracks/8/imported = true
tracks/8/enabled = true
tracks/8/path = NodePath("Armature/Skeleton3D:foot.L")
tracks/8/interp = 1
tracks/8/loop_wrap = true
tracks/8/keys = PackedFloat32Array(0, 1, 0.261968, 0.0834551, 0.265751, 0.924004)
tracks/9/type = "rotation_3d"
tracks/9/imported = true
tracks/9/enabled = true
tracks/9/path = NodePath("Armature/Skeleton3D:foot_2.L")
tracks/9/interp = 1
tracks/9/loop_wrap = true
tracks/9/keys = PackedFloat32Array(0, 1, 3.84334e-08, 0.690882, 0.722967, 1.64515e-08, 0.0333333, 1, 3.55166e-08, 0.673012, 0.739631, 2.06281e-08, 0.0666667, 1, 4.017e-08, 0.650774, 0.759271, 2.5428e-08, 0.133333, 1, 3.47263e-08, 0.60042, 0.799685, 2.08202e-08, 0.166667, 1, 3.25479e-08, 0.576432, 0.817145, 1.90111e-08, 0.2, 1, 3.92535e-08, 0.556296, 0.830985, 2.60857e-08, 0.233333, 1, 2.84263e-08, 0.542441, 0.840094, 2.02176e-08, 0.3, 1, 2.84263e-08, 0.542441, 0.840094, 2.02176e-08, 0.333333, 1, 3.40507e-08, 0.556296, 0.830985, 2.08829e-08, 0.366667, 1, 3.25479e-08, 0.576432, 0.817145, 1.90111e-08, 0.4, 1, 1.85956e-08, 0.60042, 0.799685, 2.6308e-08, 0.466667, 1, 4.017e-08, 0.650774, 0.759271, 2.5428e-08, 0.5, 1, 3.55166e-08, 0.673012, 0.739631, 2.06281e-08, 0.533333, 1, 3.33283e-08, 0.690882, 0.722967, 2.18859e-08, 0.566667, 1, 3.86219e-08, 0.702774, 0.711413, 2.28649e-08, 0.633333, 1, 3.82926e-08, 0.702774, 0.711413, 2.25356e-08, 0.666667, 1, 3.84334e-08, 0.690882, 0.722967, 1.64515e-08, 0.7, 1, 3.55166e-08, 0.673012, 0.739631, 2.06281e-08, 0.733333, 1, 4.017e-08, 0.650774, 0.759271, 2.5428e-08, 0.8, 1, 3.47263e-08, 0.60042, 0.799685, 2.08202e-08, 0.833333, 1, 3.25479e-08, 0.576432, 0.817145, 1.90111e-08, 0.866667, 1, 3.92535e-08, 0.556296, 0.830985, 2.60857e-08, 0.9, 1, 2.84263e-08, 0.542441, 0.840094, 2.02176e-08, 0.966667, 1, 2.84263e-08, 0.542441, 0.840094, 2.02176e-08, 1, 1, 3.40507e-08, 0.556296, 0.830985, 2.08829e-08, 1.03333, 1, 3.25479e-08, 0.576432, 0.817145, 1.90111e-08, 1.06667, 1, 1.85956e-08, 0.60042, 0.799685, 2.6308e-08, 1.13333, 1, 4.017e-08, 0.650774, 0.759271, 2.5428e-08, 1.16667, 1, 3.55166e-08, 0.673012, 0.739631, 2.06281e-08, 1.2, 1, 3.33283e-08, 0.690882, 0.722967, 2.18859e-08, 1.23333, 1, 3.86219e-08, 0.702774, 0.711413, 2.28649e-08, 1.3, 1, 3.82926e-08, 0.702774, 0.711413, 2.25356e-08, 1.33333, 1, 3.84334e-08, 0.690882, 0.722967, 1.64515e-08)
tracks/10/type = "position_3d"
tracks/10/imported = true
tracks/10/enabled = true
tracks/10/path = NodePath("Armature/Skeleton3D:foot.R")
tracks/10/interp = 1
tracks/10/loop_wrap = true
tracks/10/keys = PackedFloat32Array(0, 1, -0.130992, -0.227392, -0.0878515)
tracks/11/type = "rotation_3d"
tracks/11/imported = true
tracks/11/enabled = true
tracks/11/path = NodePath("Armature/Skeleton3D:foot.R")
tracks/11/interp = 1
tracks/11/loop_wrap = true
tracks/11/keys = PackedFloat32Array(0, 1, 0.261968, -0.0834551, -0.265751, 0.924004)
tracks/12/type = "rotation_3d"
tracks/12/imported = true
tracks/12/enabled = true
tracks/12/path = NodePath("Armature/Skeleton3D:foot_2.R")
tracks/12/interp = 1
tracks/12/loop_wrap = true
tracks/12/keys = PackedFloat32Array(0, 1, 2.54238e-08, 0.690882, 0.722967, 3.63776e-08, 0.0333333, 1, 2.11774e-08, 0.673013, 0.739631, 4.78233e-08, 0.0666667, 1, 2.49787e-08, 0.650774, 0.759271, 3.23632e-08, 0.133333, 1, 3.67218e-08, 0.60042, 0.799685, 4.70949e-08, 0.166667, 1, 2.63662e-08, 0.576432, 0.817145, 4.85832e-08, 0.2, 1, 2.33095e-08, 0.556296, 0.830984, 3.56522e-08, 0.233333, 1, 2.52265e-08, 0.542441, 0.840094, 4.39632e-08, 0.3, 1, 2.52265e-08, 0.542441, 0.840094, 4.39632e-08, 0.333333, 1, 2.31416e-08, 0.556296, 0.830984, 3.54843e-08, 0.366667, 1, 2.63662e-08, 0.576432, 0.817145, 4.85832e-08, 0.4, 1, 3.15666e-08, 0.60042, 0.799685, 3.09642e-08, 0.466667, 1, 2.49787e-08, 0.650774, 0.759271, 3.23632e-08, 0.5, 1, 2.11774e-08, 0.673013, 0.739631, 4.78233e-08, 0.533333, 1, 2.52591e-08, 0.690882, 0.722967, 3.62129e-08, 0.566667, 1, 2.11702e-08, 0.702774, 0.711413, 3.50482e-08, 0.633333, 1, 2.14994e-08, 0.702774, 0.711413, 3.53775e-08, 0.666667, 1, 2.54238e-08, 0.690882, 0.722967, 3.63776e-08, 0.7, 1, 2.11774e-08, 0.673013, 0.739631, 4.78233e-08, 0.733333, 1, 2.49787e-08, 0.650774, 0.759271, 3.23632e-08, 0.8, 1, 3.67218e-08, 0.60042, 0.799685, 4.70949e-08, 0.833333, 1, 2.63662e-08, 0.576432, 0.817145, 4.85832e-08, 0.866667, 1, 2.33095e-08, 0.556296, 0.830984, 3.56522e-08, 0.9, 1, 2.52265e-08, 0.542441, 0.840094, 4.39632e-08, 0.966667, 1, 2.52265e-08, 0.542441, 0.840094, 4.39632e-08, 1, 1, 2.31416e-08, 0.556296, 0.830984, 3.54843e-08, 1.03333, 1, 2.63662e-08, 0.576432, 0.817145, 4.85832e-08, 1.06667, 1, 3.15666e-08, 0.60042, 0.799685, 3.09642e-08, 1.13333, 1, 2.49787e-08, 0.650774, 0.759271, 3.23632e-08, 1.16667, 1, 2.11774e-08, 0.673013, 0.739631, 4.78233e-08, 1.2, 1, 2.52591e-08, 0.690882, 0.722967, 3.62129e-08, 1.23333, 1, 2.11702e-08, 0.702774, 0.711413, 3.50482e-08, 1.3, 1, 2.14994e-08, 0.702774, 0.711413, 3.53775e-08, 1.33333, 1, 2.54238e-08, 0.690882, 0.722967, 3.63776e-08)

[sub_resource type="Animation" id="Animation_vgr6s"]
length = 0.001
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("Armature/Skeleton3D/bat:scale")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Vector3(1, 1, 1)]
}
tracks/1/type = "value"
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/path = NodePath("Armature/Skeleton3D/bat:surface_material_override/0:albedo_color")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Color(1, 1, 1, 1)]
}

[sub_resource type="AnimationLibrary" id="AnimationLibrary_2h6wp"]
_data = {
&"Idle": SubResource("Animation_0gbdx"),
&"RESET": SubResource("Animation_vgr6s")
}

[sub_resource type="Animation" id="Animation_1he7g"]
length = 0.2
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("Armature/Skeleton3D/bat:scale")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0, 0.1, 0.2),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [Vector3(1, 1, 1), Vector3(1.435, 1.435, 1.435), Vector3(1, 1, 1)]
}
tracks/1/type = "value"
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/path = NodePath("Armature/Skeleton3D/bat:surface_material_override/0:albedo_color")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/keys = {
"times": PackedFloat32Array(0, 0.1, 0.2),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [Color(1, 1, 1, 1), Color(0.866469, 0.288918, 0.347136, 1), Color(1, 1, 1, 1)]
}

[sub_resource type="AnimationLibrary" id="AnimationLibrary_1he7g"]
_data = {
&"hurt": SubResource("Animation_1he7g")
}

[sub_resource type="AnimationNodeAnimation" id="AnimationNodeAnimation_shx3p"]
animation = &"custom/hurt"

[sub_resource type="AnimationNodeAnimation" id="AnimationNodeAnimation_scfiv"]
animation = &"Idle"

[sub_resource type="AnimationNodeOneShot" id="AnimationNodeOneShot_qf8hm"]
mix_mode = 1

[sub_resource type="AnimationNodeBlendTree" id="AnimationNodeBlendTree_030dj"]
graph_offset = Vector2(-508.233, -21.5939)
nodes/Hurt/node = SubResource("AnimationNodeAnimation_shx3p")
nodes/Hurt/position = Vector2(-140, 240)
nodes/Idle/node = SubResource("AnimationNodeAnimation_scfiv")
nodes/Idle/position = Vector2(-140, 60)
nodes/OneShot/node = SubResource("AnimationNodeOneShot_qf8hm")
nodes/OneShot/position = Vector2(80, 100)
node_connections = [&"OneShot", 0, &"Idle", &"OneShot", 1, &"Hurt", &"output", 0, &"OneShot"]

[node name="bat_model" instance=ExtResource("1_1he7g")]
script = ExtResource("2_vgr6s")

[node name="Skeleton3D" parent="Armature" index="0"]
bones/0/position = Vector3(0, -0.0173904, 0)
bones/1/rotation = Quaternion(-0.0565796, 0.212554, -0.671126, 0.707961)
bones/2/rotation = Quaternion(-0.131902, -0.0354386, 0.257414, 0.9566)
bones/3/rotation = Quaternion(-0.0487295, 0.0363842, 0.235416, 0.96999)
bones/4/rotation = Quaternion(-0.0565796, -0.212554, 0.671126, 0.707961)
bones/5/rotation = Quaternion(-0.131902, 0.0354386, -0.257414, 0.9566)
bones/6/rotation = Quaternion(-0.0487295, -0.0363842, -0.235416, 0.96999)
bones/7/position = Vector3(0.130992, -0.227392, -0.0878515)
bones/7/rotation = Quaternion(0.261968, 0.0834552, 0.265751, 0.924005)
bones/8/rotation = Quaternion(3.81718e-08, 0.660415, 0.750901, 2.33651e-08)
bones/10/position = Vector3(-0.130992, -0.227392, -0.0878515)
bones/10/rotation = Quaternion(0.261968, -0.0834552, -0.265751, 0.924005)
bones/11/rotation = Quaternion(2.33455e-08, 0.660415, 0.750901, 3.90199e-08)

[node name="bat" parent="Armature/Skeleton3D" index="0"]
surface_material_override/0 = ExtResource("2_030dj")

[node name="AnimationPlayer" parent="." index="1"]
libraries = {
&"": SubResource("AnimationLibrary_2h6wp"),
&"custom": SubResource("AnimationLibrary_1he7g")
}

[node name="AnimationTree" type="AnimationTree" parent="." index="2"]
unique_name_in_owner = true
root_node = NodePath("%AnimationTree/..")
tree_root = SubResource("AnimationNodeBlendTree_030dj")
anim_player = NodePath("../AnimationPlayer")
parameters/OneShot/active = false
parameters/OneShot/internal_active = false
parameters/OneShot/request = 0
