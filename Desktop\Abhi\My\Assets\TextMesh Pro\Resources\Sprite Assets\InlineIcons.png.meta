fileFormatVersion: 2
guid: 215687feeab0b7445b4a1cd86c82c2e1
labels:
- IgnoreTest_CheckPlatformTextureSettings
TextureImporter:
  internalIDToNameTable:
  - first:
      213: 21300000
    second: premium
  - first:
      213: 21300002
    second: coins
  - first:
      213: 21300004
    second: food
  - first:
      213: 21300006
    second: goods
  - first:
      213: 21300008
    second: glass
  - first:
      213: 21300010
    second: workers_big_city_capital
  - first:
      213: 21300012
    second: research_points
  - first:
      213: 21300014
    second: boost
  - first:
      213: 21300016
    second: bb_checkmark
  - first:
      213: -6279640769779937744
    second: clock_embossed_pos
  - first:
      213: 2102306210881839429
    second: culture_bonus
  - first:
      213: 4513387777306473356
    second: workers_small_city_capital
  - first:
      213: -6479823341794910233
    second: expansion
  - first:
      213: 7780953735995972703
    second: happiness_3
  - first:
      213: 7459902675860876989
    second: culture_range
  - first:
      213: -399840562033327265
    second: map
  - first:
      213: 1357911645972831016
    second: scout
  - first:
      213: 1260782343283211980
    second: swords
  - first:
      213: 7096138885364736195
    second: upgrade
  - first:
      213: 2441754920960949527
    second: coin_food_boost
  - first:
      213: -3338912914735402841
    second: range_blue
  - first:
      213: 8323598589155270872
    second: map_blue
  - first:
      213: -8863619430189855094
    second: swords_blue
  - first:
      213: 6644182187024332451
    second: scout_blue
  - first:
      213: -3518875668666682211
    second: culture_bonus
  - first:
      213: -1869673367297931309
    second: coins_small
  - first:
      213: 7245690677989368111
    second: bb_lock
  - first:
      213: 2267314249368087281
    second: happiness_0
  - first:
      213: -9053465508694820594
    second: happiness_1
  - first:
      213: -1134528729738406305
    second: happiness_2
  - first:
      213: 4721570850333449918
    second: tutorial_icon_build
  - first:
      213: 1954612522021269676
    second: tutorial_icon_produce
  - first:
      213: -2043815690625100865
    second: tutorial_icon_incident
  - first:
      213: 7245690677989368111
    second: dead
  - first:
      213: 7245690677989368111
    second: clock_blue
  - first:
      213: 3121471347900928983
    second: workers_city_capital
  - first:
      213: 255080986811049931
    second: food_small
  - first:
      213: -8988441530965576436
    second: upgrade_small
  - first:
      213: 6547572931748238266
    second: research_points_small
  - first:
      213: -1571012651542167539
    second: warning
  - first:
      213: 7237261532831544450
    second: icon_bronze_bracelet
  - first:
      213: 8373675954752092734
    second: icon_alabaster_idol
  - first:
      213: 358799654582701605
    second: icon_wool
  - first:
      213: 1438499179017281121
    second: icon_flint
  - first:
      213: 5908195551155024309
    second: icon_hide
  - first:
      213: -7191163486456543192
    second: wonder_active_checkmark
  - first:
      213: 6905562749588875414
    second: cta_recruit
  - first:
      213: 8706619048834826526
    second: icon_food_black
  - first:
      213: -8078951251822203351
    second: irrigation_bonus
  - first:
      213: 8170858238558910055
    second: InlineIcons_1
  - first:
      213: 8806383704849223010
    second: InlineIcons_2
  - first:
      213: -4770960272869957185
    second: cta_linen_shirt
  - first:
      213: -9021633436630479557
    second: cta_marble_bust
  - first:
      213: -2269303870351376580
    second: cta_golden_sphinx
  - first:
      213: 541087786881136906
    second: cta_papyrus
  - first:
      213: -7343220986905845580
    second: cta_ankh
  - first:
      213: -2518806979871309895
    second: cta_ceremonial_dress
  - first:
      213: 6619405697063382928
    second: irrigation_0
  - first:
      213: -5368687836300328278
    second: irrigation_1
  - first:
      213: 1461871564780703198
    second: irrigation_2
  - first:
      213: -2457531169152919533
    second: irrigation
  - first:
      213: -8318277596760801014
    second: cta_iron_pendant
  - first:
      213: 2117336584302238645
    second: cta_gold_ore
  - first:
      213: -7215298494019262123
    second: cta_papyrus_scroll
  - first:
      213: 8674190453677158122
    second: cta_research_points
  - first:
      213: 1494432333228735887
    second: workers_trading_big
  - first:
      213: -6162265055754030298
    second: cta_silver_ring
  - first:
      213: -7230867332484785407
    second: cta_toga
  - first:
      213: 8640258737205591229
    second: cta_column
  - first:
      213: -7927273033115742964
    second: deben
  - first:
      213: -7357144505842695810
    second: cta_gold_laurel
  - first:
      213: 210297501773229927
    second: cta_tunic
  - first:
      213: 3537169958838929854
    second: cta_stone_tablet
  - first:
      213: 4104117333016806175
    second: ebc_cryptofthecount
  - first:
      213: 3495086814825567685
    second: blueprint
  - first:
      213: 921062424029211809
    second: workers_big_city_egypt
  - first:
      213: -3402344481944718353
    second: trade
  - first:
      213: 7972151216418658502
    second: InlineIcons_32
  - first:
      213: 5078477160627523986
    second: wonders
  - first:
      213: -497568770147540219
    second: unit_cavalry
  - first:
      213: 5390246818323809478
    second: unit_range
  - first:
      213: 2437483765381717123
    second: unit_melee
  - first:
      213: -5437674158304577141
    second: commander
  - first:
      213: -5645743825326872590
    second: icon_trade token
  - first:
      213: -701876230672350822
    second: cta_building_vikings_sailorport
  - first:
      213: -7851600766591116406
    second: cta_silk_threads
  - first:
      213: -2829443050583330228
    second: cta_rice
  - first:
      213: 4524241383834298874
    second: cta_silk
  - first:
      213: 6396128496709196802
    second: cta_porcelain
  - first:
      213: -7411174152488024004
    second: cta_clay
  - first:
      213: -44565893461480510
    second: cta_moth_cocoons
  - first:
      213: 4936506583271141030
    second: cta_kaolin
  - first:
      213: -8264326421717354311
    second: cta_wu_zhu
  - first:
      213: -5167353112666354359
    second: cta_coins
  - first:
      213: -1321098932929957079
    second: cta_mosaic
  - first:
      213: 7462434613906369459
    second: cta_goblet
  - first:
      213: 8287165777748813492
    second: cta_cape
  - first:
      213: -7907368816393526887
    second: wu_zhu
  - first:
      213: 5803713721798503446
    second: cta_cocoa
  - first:
      213: 6409830552331150148
    second: cta_jade
  - first:
      213: 4956319499149316874
    second: cta_obsidian
  - first:
      213: 8757602080057577540
    second: cta_feather
  - first:
      213: 9112743452827066768
    second: cta_priest
  - first:
      213: -5159087384432534610
    second: cta_ancestor_mask
  - first:
      213: 8678780475385641507
    second: cta_calendar_stone
  - first:
      213: -5049782452158657568
    second: cta_ritual_dagger
  - first:
      213: 7513239266779225853
    second: cta_headdress
  - first:
      213: -6316392241553622110
    second: cta_suns_blessing
  - first:
      213: 6195470969790340212
    second: ranking
  - first:
      213: -4834095905783300709
    second: ebc_hydra
  - first:
      213: 6107784321054212483
    second: cocoa
  - first:
      213: 7681201850867341242
    second: ath_attempt
  - first:
      213: -7043394559850540028
    second: cta_obsidianjade
  - first:
      213: -4501791974573854292
    second: cta_obsidiancalendarancestormask
  - first:
      213: 5488086946749717670
    second: cta_pepper
  - first:
      213: -1670260634343250872
    second: cta_planks
  - first:
      213: -8210193684748564459
    second: cta_salt
  - first:
      213: -2217656884674354197
    second: cta_ink
  - first:
      213: -8037489388941252367
    second: cta_wooden_wheel
  - first:
      213: 1023881543254830229
    second: cta_happiness_maya_2
  - first:
      213: -8141631665637155745
    second: workers_big_priestmaya
  - first:
      213: -8943414918624194942
    second: workers_big_priestmaya
  - first:
      213: -4198053654051914648
    second: workers_small_priestmaya
  - first:
      213: -2136406889496393637
    second: workers_big_city_mayas
  - first:
      213: 6228371465862800648
    second: workers_city_mayas
  - first:
      213: 2108014542765709681
    second: workers_small_city_mayas
  - first:
      213: -6707575059094875716
    second: workers_big_city_china
  - first:
      213: 5706957933644387078
    second: workers_city_china
  - first:
      213: -3297204628491984772
    second: workers_small_city_china
  - first:
      213: -6346629593138849361
    second: workers_city_egypt
  - first:
      213: -3389039848645536794
    second: workers_small_city_egypt
  - first:
      213: 37763770702065993
    second: ebc_madscientistslab
  - first:
      213: -1999946806344817058
    second: ebc_piratefortress
  - first:
      213: 7812820661483254461
    second: wonder_inactive_checkmark
  - first:
      213: 3969048961352433308
    second: cta_maya_luxurious_workshop_level2
  - first:
      213: 1037585894331930479
    second: ebc_wintermarket
  - first:
      213: 1853125249586873685
    second: info
  - first:
      213: 9124709818946717747
    second: ebc_trojanhorse
  - first:
      213: -8267115305123039760
    second: icon_chest_good2
  - first:
      213: 8996665824694350818
    second: ebc_broch
  - first:
      213: 4624541004163449478
    second: cta_manuscript
  - first:
      213: 2289998852469363648
    second: cta_barrel
  - first:
      213: -7333887034695731682
    second: cta_herbs
  - first:
      213: 1088730770723027953
    second: cta_icon_chest_blueprint
  - first:
      213: -1953511547368589529
    second: cta_icon_chest_rp
  - first:
      213: 4939863863825205819
    second: ebc_persianpalace
  - first:
      213: -1104962026362419977
    second: shoreline
  - first:
      213: -2794259043635821732
    second: cta_mead
  - first:
      213: 8617892559191809022
    second: cta_stockfish
  - first:
      213: 5701253759128972283
    second: cta_spice_treasure
  - first:
      213: 8814212362280375727
    second: cta_gold_treasure
  - first:
      213: -4951088303895349411
    second: icon_gem_treasure
  - first:
      213: -1856732651501631057
    second: cta_pennies
  - first:
      213: -3386218922703161388
    second: cta_honey
  - first:
      213: -8478562381375844705
    second: cta_fish
  - first:
      213: 4751259173209894178
    second: cta_ceramic_treasure
  - first:
      213: 7700661865242328817
    second: pennies
  - first:
      213: 8289822419994577490
    second: workers_big_city_vikings
  - first:
      213: 3017109405843695886
    second: workers_city_vikings
  - first:
      213: 429688389500101892
    second: workers_small_city_vikings
  - first:
      213: 7673711170268496860
    second: workers_big_sailorvikings
  - first:
      213: 7291930161081552924
    second: workers_sailorvikings
  - first:
      213: -983479635111419447
    second: workers_small_sailorvikings
  - first:
      213: -5617448299673312401
    second: cta_mixedchest
  - first:
      213: -2904807348406626498
    second: wonder_orb
  - first:
      213: -1487877384612313618
    second: cta_icon_mystery_chest_gold
  - first:
      213: -1659708932042841261
    second: blueprint_rare
  - first:
      213: 4281911320752687849
    second: InlineIcons_122
  - first:
      213: 7674156195484195045
    second: gears
  - first:
      213: -7006899675943808146
    second: ebc_madrasa
  - first:
      213: 5534174253454217892
    second: ebc_shrineofreflection
  - first:
      213: 4642325231336661812
    second: cta_gears
  - first:
      213: 3375157875343467442
    second: cta_wax_seal
  - first:
      213: -838908008875007501
    second: cta_door
  - first:
      213: -2578651400172810937
    second: cta_saffron
  - first:
      213: -4178829386858457095
    second: InlineIcons_130
  - first:
      213: 2586379074099917112
    second: cta_building_vikings_sailorport_premium_1
  - first:
      213: -12563651127225671
    second: cta_building_vikings_sailorport_premium_2
  - first:
      213: 3112621357950915070
    second: InlineIcons_133
  - first:
      213: -329868462160931304
    second: InlineIcons_134
  - first:
      213: -8463493406355435034
    second: InlineIcons_135
  - first:
      213: 2084413756825693856
    second: InlineIcons_136
  - first:
      213: 3643772185187624922
    second: flat_cancel
  - first:
      213: -922888878065087515
    second: flat_check
  - first:
      213: 2061223666325461347
    second: bb_lock_grey
  - first:
      213: -2965744954758517795
    second: icon_crate
  - first:
      213: 3436914362765528371
    second: icon_star
  - first:
      213: 7134325258175837443
    second: icon_tome
  - first:
      213: -728244194544253039
    second: icon_wardrobe
  - first:
      213: -5750983614340588109
    second: icon_chili
  - first:
      213: -4020335626915314013
    second: icon_arabia_worker
  - first:
      213: 3814798381351036019
    second: icon_dirham
  - first:
      213: 1302763225339234619
    second: icon_gold_dinar
  - first:
      213: 1896382066041297041
    second: icon_incense
  - first:
      213: -6463064620358863643
    second: icon_coffee_beans
  - first:
      213: -7081792888699921481
    second: icon_myrrh
  - first:
      213: 8475544577015182143
    second: icon_brass
  - first:
      213: 6694968190869980855
    second: icon_coffee
  - first:
      213: 4932850884273308405
    second: icon_camel
  - first:
      213: -5536544251107829900
    second: InlineIcons_154
  - first:
      213: 8954016801280762372
    second: cta_icon_mystery_chest
  - first:
      213: -7335904089167868400
    second: cta_icon_loot_container
  - first:
      213: -3662145209645026858
    second: cta_icon_loot_container_bronze
  - first:
      213: 2101787439100353768
    second: InlineIcons_158
  - first:
      213: 6718763151315418932
    second: icon_event_pegasus_tokens
  - first:
      213: 8158563787144471110
    second: icon_event_greek_2023_grand_prize_progress
  externalObjects: {}
  serializedVersion: 12
  mipmaps:
    mipMapMode: 0
    enableMipMap: 0
    sRGBTexture: 1
    linearTexture: 0
    fadeOut: 0
    borderMipMap: 0
    mipMapsPreserveCoverage: 0
    alphaTestReferenceValue: 0.5
    mipMapFadeDistanceStart: 1
    mipMapFadeDistanceEnd: 3
  bumpmap:
    convertToNormalMap: 0
    externalNormalMap: 0
    heightScale: 0.25
    normalMapFilter: 0
  isReadable: 0
  streamingMipmaps: 0
  streamingMipmapsPriority: 0
  vTOnly: 0
  ignoreMasterTextureLimit: 0
  grayScaleToAlpha: 0
  generateCubemap: 6
  cubemapConvolution: 0
  seamlessCubemap: 0
  textureFormat: 1
  maxTextureSize: 2048
  textureSettings:
    serializedVersion: 2
    filterMode: 1
    aniso: 1
    mipBias: 0
    wrapU: 1
    wrapV: 1
    wrapW: 0
  nPOTScale: 0
  lightmap: 0
  compressionQuality: 50
  spriteMode: 2
  spriteExtrude: 1
  spriteMeshType: 1
  alignment: 0
  spritePivot: {x: 0.5, y: 0.5}
  spritePixelsToUnits: 100
  spriteBorder: {x: 0, y: 0, z: 0, w: 0}
  spriteGenerateFallbackPhysicsShape: 0
  alphaUsage: 1
  alphaIsTransparency: 1
  spriteTessellationDetail: -1
  textureType: 8
  textureShape: 1
  singleChannelComponent: 0
  flipbookRows: 1
  flipbookColumns: 1
  maxTextureSizeSet: 0
  compressionQualitySet: 0
  textureFormatSet: 0
  ignorePngGamma: 0
  applyGammaDecoding: 1
  cookieLightType: 1
  platformSettings:
  - serializedVersion: 3
    buildTarget: DefaultTexturePlatform
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 2
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 3
    buildTarget: Standalone
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 2
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 3
    buildTarget: WebGL
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: 29
    textureCompression: 2
    compressionQuality: 100
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 1
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 3
    buildTarget: iPhone
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: 48
    textureCompression: 2
    compressionQuality: 100
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 1
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 3
    buildTarget: Android
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: 47
    textureCompression: 2
    compressionQuality: 100
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 1
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 3
    buildTarget: Server
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 2
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  spriteSheet:
    serializedVersion: 2
    sprites:
    - serializedVersion: 2
      name: premium
      rect:
        serializedVersion: 2
        x: 49
        y: 866
        width: 76
        height: 92
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 83f85091368767e4188587d328b8c84f
      internalID: 21300000
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: coins
      rect:
        serializedVersion: 2
        x: 158
        y: 864
        width: 85
        height: 93
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 49e741622c3aefa4ca44ec32dad4ef71
      internalID: 21300002
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: food
      rect:
        serializedVersion: 2
        x: 278
        y: 867
        width: 87
        height: 94
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 7d7bc22a6fb83004d8764e921be63edd
      internalID: 21300004
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: goods
      rect:
        serializedVersion: 2
        x: 398
        y: 862
        width: 107
        height: 88
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: d89852f581cf9354e8fb5aa021cc68e3
      internalID: 21300006
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: glass
      rect:
        serializedVersion: 2
        x: 505
        y: 848
        width: 103
        height: 105
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 53f810b2f7a935246a387957ace8ad2f
      internalID: 21300008
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: workers_big_city_capital
      rect:
        serializedVersion: 2
        x: 53
        y: 729
        width: 98
        height: 93
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 4cd4703620da72440a97a394f2eac0e0
      internalID: 21300010
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: research_points
      rect:
        serializedVersion: 2
        x: 234
        y: 734
        width: 78
        height: 92
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: aa8f66d69aacd5a44b855a1defaa1136
      internalID: 21300012
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: boost
      rect:
        serializedVersion: 2
        x: 333
        y: 742
        width: 79
        height: 79
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 2b0d033be79806440bcba6231f98aba1
      internalID: 21300014
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: bb_checkmark
      rect:
        serializedVersion: 2
        x: 427
        y: 760
        width: 40
        height: 38
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: c563e19d8f40c7d4088db86f37a4c600
      internalID: 21300016
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: clock_embossed_pos
      rect:
        serializedVersion: 2
        x: 478
        y: 759
        width: 40
        height: 42
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: ced74530b2ea39a4ab157a71997e2f8b
      internalID: -6279640769779937744
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: workers_small_city_capital
      rect:
        serializedVersion: 2
        x: 152
        y: 730
        width: 56
        height: 50
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 9d726b4690cc3c046a3d3625cc4b2d45
      internalID: 4513387777306473356
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: expansion
      rect:
        serializedVersion: 2
        x: 609
        y: 864
        width: 84
        height: 77
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: a53c7f54d885517428d30f4f28fce080
      internalID: -6479823341794910233
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: happiness_3
      rect:
        serializedVersion: 2
        x: 712
        y: 865
        width: 81
        height: 86
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 68cb8f35ec89aae49b141f263f832673
      internalID: 7780953735995972703
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: culture_range
      rect:
        serializedVersion: 2
        x: 808
        y: 862
        width: 86
        height: 93
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 3bcc40f1032b13945baef86763ef6aed
      internalID: 7459902675860876989
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: map
      rect:
        serializedVersion: 2
        x: 906
        y: 870
        width: 84
        height: 74
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: dabd4aac492f3a944bff918910a2b10c
      internalID: -399840562033327265
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: scout
      rect:
        serializedVersion: 2
        x: 905
        y: 784
        width: 83
        height: 56
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 2b4f5de59fe9d214cb8e9f2b2ea74681
      internalID: 1357911645972831016
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: swords
      rect:
        serializedVersion: 2
        x: 819
        y: 778
        width: 65
        height: 67
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 5a9b475af385adc40b094c5de24f73b0
      internalID: 1260782343283211980
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: upgrade
      rect:
        serializedVersion: 2
        x: 720
        y: 774
        width: 66
        height: 80
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 2e9e84f41137bc9459155c51b48a78cf
      internalID: 7096138885364736195
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: coin_food_boost
      rect:
        serializedVersion: 2
        x: 642
        y: 625
        width: 154
        height: 106
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: b5e1b5d0bc69e9b4e84a618d58c0184f
      internalID: 2441754920960949527
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: culture_bonus
      rect:
        serializedVersion: 2
        x: 623
        y: 741
        width: 76
        height: 74
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 436398409ee116b4f8cf892406ccb78b
      internalID: 2102306210881839429
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: coins_small
      rect:
        serializedVersion: 2
        x: 169
        y: 957
        width: 60
        height: 66
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 8ca9c23c24295e645bf4cc9cc4d0bd57
      internalID: -1869673367297931309
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: bb_lock
      rect:
        serializedVersion: 2
        x: 430
        y: 800
        width: 34
        height: 47
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 37d6aeaf2a1d1f24eaba5154ac8b0e13
      internalID: 7245690677989368111
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: happiness_0
      rect:
        serializedVersion: 2
        x: 58
        y: 636
        width: 78
        height: 84
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 4ad54f0f6dbfb7143bad98c648e809ca
      internalID: 2267314249368087281
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: happiness_1
      rect:
        serializedVersion: 2
        x: 137
        y: 636
        width: 78
        height: 84
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 6ec6fcaba52d4494faf75bc1c3af336c
      internalID: -9053465508694820594
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: happiness_2
      rect:
        serializedVersion: 2
        x: 216
        y: 636
        width: 78
        height: 84
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: ec13a36d203f80941870232804ccef92
      internalID: -1134528729738406305
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: tutorial_icon_build
      rect:
        serializedVersion: 2
        x: 0
        y: 507
        width: 94
        height: 110
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: fd561678281081f438b6638ea4235756
      internalID: 4721570850333449918
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: tutorial_icon_produce
      rect:
        serializedVersion: 2
        x: 95
        y: 507
        width: 105
        height: 110
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 2afac35630599f147977221b5b90f941
      internalID: 1954612522021269676
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: tutorial_icon_incident
      rect:
        serializedVersion: 2
        x: 200
        y: 507
        width: 133
        height: 110
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 9a374c56294889d49863ffcd54fada26
      internalID: -2043815690625100865
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: dead
      rect:
        serializedVersion: 2
        x: 310
        y: 628
        width: 111
        height: 80
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: a0158a702588ee3459817f1e7556e159
      internalID: -9160987577138628238
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: clock_blue
      rect:
        serializedVersion: 2
        x: 429
        y: 661
        width: 90
        height: 91
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: cb6c77a2aa025d843be0f74568128068
      internalID: -8246627260571945669
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: workers_city_capital
      rect:
        serializedVersion: 2
        x: 0
        y: 960
        width: 69
        height: 65
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: b32f1b9cf20a22842a506a3deca6ce00
      internalID: 3121471347900928983
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: food_small
      rect:
        serializedVersion: 2
        x: 357
        y: 952
        width: 61
        height: 68
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 77228c1a149c01c47bd9c44a2fbce206
      internalID: 255080986811049931
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: upgrade_small
      rect:
        serializedVersion: 2
        x: 483
        y: 959
        width: 55
        height: 65
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: f55aebf2655497f4fae10f5f71b0f3a1
      internalID: -8988441530965576436
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: research_points_small
      rect:
        serializedVersion: 2
        x: 162
        y: 786
        width: 56
        height: 67
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: bcf88ff28388b57408808d682fe07859
      internalID: 6547572931748238266
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: warning
      rect:
        serializedVersion: 2
        x: 607
        y: 969
        width: 56
        height: 49
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: d004f272154a23ae0800000000000000
      internalID: -1571012651542167539
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: icon_bronze_bracelet
      rect:
        serializedVersion: 2
        x: 342
        y: 530
        width: 75
        height: 92
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 284f50bf40bef6460800000000000000
      internalID: 7237261532831544450
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: icon_alabaster_idol
      rect:
        serializedVersion: 2
        x: 417
        y: 530
        width: 75
        height: 92
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: e3a7a3b4ae5453470800000000000000
      internalID: 8373675954752092734
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: icon_wool
      rect:
        serializedVersion: 2
        x: 492
        y: 531
        width: 92
        height: 92
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 52e4fe48266baf400800000000000000
      internalID: 358799654582701605
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: icon_flint
      rect:
        serializedVersion: 2
        x: 584
        y: 530
        width: 82
        height: 92
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 16e140fb16396f310800000000000000
      internalID: 1438499179017281121
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: icon_hide
      rect:
        serializedVersion: 2
        x: 668
        y: 531
        width: 93
        height: 90
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 5b173220d802ef150800000000000000
      internalID: 5908195551155024309
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: cta_recruit
      rect:
        serializedVersion: 2
        x: 529
        y: 638
        width: 83
        height: 100
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 6982a67cbbc75df50800000000000000
      internalID: 6905562749588875414
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: icon_food_black
      rect:
        serializedVersion: 2
        x: 841
        y: 533
        width: 81
        height: 90
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: e11037035ef14d870800000000000000
      internalID: 8706619048834826526
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: irrigation_bonus
      rect:
        serializedVersion: 2
        x: 546
        y: 748
        width: 60
        height: 72
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 92ea07df3acc1ef80800000000000000
      internalID: -8078951251822203351
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: cta_linen_shirt
      rect:
        serializedVersion: 2
        x: 60
        y: 412
        width: 75
        height: 94
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: fb1e512d8e42acdb0800000000000000
      internalID: -4770960272869957185
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: cta_marble_bust
      rect:
        serializedVersion: 2
        x: 461
        y: 415
        width: 71
        height: 93
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: b3d8ad32038bcc280800000000000000
      internalID: -9021633436630479557
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: cta_golden_sphinx
      rect:
        serializedVersion: 2
        x: 137
        y: 413
        width: 91
        height: 92
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: c3f79483720d180e0800000000000000
      internalID: -2269303870351376580
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: cta_papyrus
      rect:
        serializedVersion: 2
        x: 231
        y: 413
        width: 92
        height: 93
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: a093a51b974528700800000000000000
      internalID: 541087786881136906
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: cta_ankh
      rect:
        serializedVersion: 2
        x: 327
        y: 417
        width: 68
        height: 93
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 4b0a8402b63a71a90800000000000000
      internalID: -7343220986905845580
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: cta_ceremonial_dress
      rect:
        serializedVersion: 2
        x: 396
        y: 417
        width: 60
        height: 93
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 9bffef2a5666b0dd0800000000000000
      internalID: -2518806979871309895
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: irrigation_0
      rect:
        serializedVersion: 2
        x: 0
        y: 325
        width: 78
        height: 84
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 09fe8ad096adcdb50800000000000000
      internalID: 6619405697063382928
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: irrigation_1
      rect:
        serializedVersion: 2
        x: 79
        y: 325
        width: 78
        height: 84
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: aa2cb5f7bd69e75b0800000000000000
      internalID: -5368687836300328278
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: irrigation_2
      rect:
        serializedVersion: 2
        x: 159
        y: 325
        width: 78
        height: 84
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: edd8ffd517c994410800000000000000
      internalID: 1461871564780703198
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: irrigation
      rect:
        serializedVersion: 2
        x: 237
        y: 325
        width: 78
        height: 84
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 31890361c6815edd0800000000000000
      internalID: -2457531169152919533
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: cta_iron_pendant
      rect:
        serializedVersion: 2
        x: 721
        y: 417
        width: 85
        height: 93
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: a018bfd099a8f8c80800000000000000
      internalID: -8318277596760801014
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: cta_gold_ore
      rect:
        serializedVersion: 2
        x: 537
        y: 417
        width: 79
        height: 93
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 5bb1641c76a426d10800000000000000
      internalID: 2117336584302238645
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: cta_papyrus_scroll
      rect:
        serializedVersion: 2
        x: 616
        y: 417
        width: 104
        height: 93
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 559229b7f3c1edb90800000000000000
      internalID: -7215298494019262123
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: cta_research_points
      rect:
        serializedVersion: 2
        x: 808
        y: 417
        width: 80
        height: 93
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: aeee281824ae06870800000000000000
      internalID: 8674190453677158122
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: workers_trading_big
      rect:
        serializedVersion: 2
        x: 810
        y: 670
        width: 98
        height: 93
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: f8df4fa0a4a4db410800000000000000
      internalID: 1494432333228735887
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: cta_silver_ring
      rect:
        serializedVersion: 2
        x: 889
        y: 419
        width: 69
        height: 94
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 6237af3b49c3b7aa0800000000000000
      internalID: -6162265055754030298
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: cta_toga
      rect:
        serializedVersion: 2
        x: 824
        y: 318
        width: 65
        height: 95
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 10b944dc87cc6ab90800000000000000
      internalID: -7230867332484785407
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: cta_column
      rect:
        serializedVersion: 2
        x: 892
        y: 320
        width: 68
        height: 92
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: dbcba9e1b8d58e770800000000000000
      internalID: 8640258737205591229
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: deben
      rect:
        serializedVersion: 2
        x: 752
        y: 318
        width: 71
        height: 92
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: c0d4a974d2bacf190800000000000000
      internalID: -7927273033115742964
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: cta_gold_laurel
      rect:
        serializedVersion: 2
        x: 648
        y: 323
        width: 100
        height: 89
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: e7187004d0c26e990800000000000000
      internalID: -7357144505842695810
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: cta_tunic
      rect:
        serializedVersion: 2
        x: 561
        y: 312
        width: 80
        height: 100
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 76f6852b6702be200800000000000000
      internalID: 210297501773229927
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: cta_stone_tablet
      rect:
        serializedVersion: 2
        x: 482
        y: 317
        width: 76
        height: 92
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: eb907ca365d861130800000000000000
      internalID: 3537169958838929854
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: blueprint
      rect:
        serializedVersion: 2
        x: 1
        y: 241
        width: 70
        height: 72
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 5c554d8b0fa018030800000000000000
      internalID: 3495086814825567685
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: workers_big_city_egypt
      rect:
        serializedVersion: 2
        x: 914
        y: 670
        width: 94
        height: 94
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 1aca838056548cc00800000000000000
      internalID: 921062424029211809
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: trade
      rect:
        serializedVersion: 2
        x: 907
        y: 947
        width: 86
        height: 71
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: fe3bc2658b178c0d0800000000000000
      internalID: -3402344481944718353
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: InlineIcons_32
      rect:
        serializedVersion: 2
        x: 671
        y: 953
        width: 65
        height: 71
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 6c815560b45c2ae60800000000000000
      internalID: 7972151216418658502
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: wonders
      rect:
        serializedVersion: 2
        x: 77
        y: 240
        width: 101
        height: 72
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 291532712106a7640800000000000000
      internalID: 5078477160627523986
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: unit_cavalry
      rect:
        serializedVersion: 2
        x: 182
        y: 240
        width: 71
        height: 72
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 503270cf6d74819f0800000000000000
      internalID: -497568770147540219
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: unit_range
      rect:
        serializedVersion: 2
        x: 256
        y: 242
        width: 68
        height: 67
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 6c4ced92ce00eca40800000000000000
      internalID: 5390246818323809478
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: unit_melee
      rect:
        serializedVersion: 2
        x: 330
        y: 244
        width: 57
        height: 65
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 38cdb5df19ea3d120800000000000000
      internalID: 2437483765381717123
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: commander
      rect:
        serializedVersion: 2
        x: 391
        y: 240
        width: 103
        height: 71
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: b893fedb9208984b0800000000000000
      internalID: -5437674158304577141
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: icon_trade token
      rect:
        serializedVersion: 2
        x: 928
        y: 536
        width: 87
        height: 87
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 2f7698e37e946a1b0800000000000000
      internalID: -5645743825326872590
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: cta_silk_threads
      rect:
        serializedVersion: 2
        x: 960
        y: 320
        width: 50
        height: 93
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: a8bd62dc3b2890390800000000000000
      internalID: -7851600766591116406
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: cta_rice
      rect:
        serializedVersion: 2
        x: 502
        y: 212
        width: 104
        height: 92
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: c4278c9598ccbb8d0800000000000000
      internalID: -2829443050583330228
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: cta_silk
      rect:
        serializedVersion: 2
        x: 606
        y: 212
        width: 70
        height: 93
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: af9a091719559ce30800000000000000
      internalID: 4524241383834298874
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: cta_porcelain
      rect:
        serializedVersion: 2
        x: 676
        y: 215
        width: 59
        height: 92
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 20cda18cafc93c850800000000000000
      internalID: 6396128496709196802
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: cta_clay
      rect:
        serializedVersion: 2
        x: 736
        y: 221
        width: 103
        height: 72
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: c380568df58362990800000000000000
      internalID: -7411174152488024004
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: cta_moth_cocoons
      rect:
        serializedVersion: 2
        x: 840
        y: 208
        width: 92
        height: 92
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 2cb61275e8ba16ff0800000000000000
      internalID: -44565893461480510
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: cta_kaolin
      rect:
        serializedVersion: 2
        x: 935
        y: 211
        width: 78
        height: 92
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 6aee04f259ef18440800000000000000
      internalID: 4936506583271141030
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: cta_wu_zhu
      rect:
        serializedVersion: 2
        x: 0
        y: 141
        width: 96
        height: 98
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 9b879add7e63f4d80800000000000000
      internalID: -8264326421717354311
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: cta_coins
      rect:
        serializedVersion: 2
        x: 101
        y: 146
        width: 82
        height: 90
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 94d438ac8bfd948b0800000000000000
      internalID: -5167353112666354359
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: cta_mosaic
      rect:
        serializedVersion: 2
        x: 192
        y: 146
        width: 87
        height: 92
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 923ff8d15838aade0800000000000000
      internalID: -1321098932929957079
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: cta_goblet
      rect:
        serializedVersion: 2
        x: 280
        y: 145
        width: 84
        height: 93
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 3bbe7716eb4ef8760800000000000000
      internalID: 7462434613906369459
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: cta_cape
      rect:
        serializedVersion: 2
        x: 367
        y: 146
        width: 71
        height: 92
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 4b6ed791e5de10370800000000000000
      internalID: 8287165777748813492
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: wu_zhu
      rect:
        serializedVersion: 2
        x: 103
        y: 957
        width: 63
        height: 66
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 991ecded4f1634290800000000000000
      internalID: -7907368816393526887
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: cta_cocoa
      rect:
        serializedVersion: 2
        x: 451
        y: 119
        width: 56
        height: 92
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 61c2cdd61eeea8050800000000000000
      internalID: 5803713721798503446
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: cta_jade
      rect:
        serializedVersion: 2
        x: 510
        y: 119
        width: 64
        height: 91
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 443e4263dea44f850800000000000000
      internalID: 6409830552331150148
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: cta_obsidian
      rect:
        serializedVersion: 2
        x: 578
        y: 119
        width: 65
        height: 92
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: a031324235268c440800000000000000
      internalID: 4956319499149316874
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: cta_feather
      rect:
        serializedVersion: 2
        x: 646
        y: 119
        width: 42
        height: 92
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 440dd0a21b0498970800000000000000
      internalID: 8757602080057577540
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: cta_priest
      rect:
        serializedVersion: 2
        x: 696
        y: 126
        width: 76
        height: 85
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 091260393e7f67e70800000000000000
      internalID: 9112743452827066768
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: cta_ancestor_mask
      rect:
        serializedVersion: 2
        x: 844
        y: 114
        width: 87
        height: 92
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: eab13bd9b5d3768b0800000000000000
      internalID: -5159087384432534610
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: cta_calendar_stone
      rect:
        serializedVersion: 2
        x: 933
        y: 114
        width: 91
        height: 92
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 32af4f63cd8317870800000000000000
      internalID: 8678780475385641507
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: cta_ritual_dagger
      rect:
        serializedVersion: 2
        x: 1
        y: 48
        width: 66
        height: 92
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 0eb051a5d919be9b0800000000000000
      internalID: -5049782452158657568
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: cta_headdress
      rect:
        serializedVersion: 2
        x: 72
        y: 48
        width: 100
        height: 92
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: dfe54e86e43644860800000000000000
      internalID: 7513239266779225853
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: cta_suns_blessing
      rect:
        serializedVersion: 2
        x: 173
        y: 48
        width: 90
        height: 92
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 2abb924d8baa758a0800000000000000
      internalID: -6316392241553622110
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: ranking
      rect:
        serializedVersion: 2
        x: 922
        y: 38
        width: 102
        height: 72
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 47c1992a50cbaf550800000000000000
      internalID: 6195470969790340212
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: ebc_hydra
      rect:
        serializedVersion: 2
        x: 267
        y: 42
        width: 108
        height: 98
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: b9dbd95c267d9ecb0800000000000000
      internalID: -4834095905783300709
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: ath_attempt
      rect:
        serializedVersion: 2
        x: 324
        y: 330
        width: 62
        height: 76
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: ab75509f26c199a60800000000000000
      internalID: 7681201850867341242
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: cta_obsidianjade
      rect:
        serializedVersion: 2
        x: 529
        y: 16
        width: 105
        height: 101
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 404903f17f5d04e90800000000000000
      internalID: -7043394559850540028
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: cta_obsidiancalendarancestormask
      rect:
        serializedVersion: 2
        x: 637
        y: 14
        width: 141
        height: 100
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: ca132d6fc0c6681c0800000000000000
      internalID: -4501791974573854292
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: cocoa
      rect:
        serializedVersion: 2
        x: 232
        y: 958
        width: 38
        height: 64
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 3810325ca7533c450800000000000000
      internalID: 6107784321054212483
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: cta_pepper
      rect:
        serializedVersion: 2
        x: 375
        y: 35
        width: 86
        height: 93
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 6ac997d300a992c40800000000000000
      internalID: 5488086946749717670
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: cta_planks
      rect:
        serializedVersion: 2
        x: 779
        y: 24
        width: 90
        height: 83
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 844e6af6dca02d8e0800000000000000
      internalID: -1670260634343250872
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: cta_salt
      rect:
        serializedVersion: 2
        x: 3
        y: 1024
        width: 89
        height: 104
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 518086bd7588f0e80800000000000000
      internalID: -8210193684748564459
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: cta_ink
      rect:
        serializedVersion: 2
        x: 97
        y: 1026
        width: 86
        height: 96
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: be3294320dc4931e0800000000000000
      internalID: -2217656884674354197
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: cta_wooden_wheel
      rect:
        serializedVersion: 2
        x: 186
        y: 1027
        width: 95
        height: 98
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 1f86f263cf9157090800000000000000
      internalID: -8037489388941252367
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: cta_happiness_maya_2
      rect:
        serializedVersion: 2
        x: 284
        y: 1027
        width: 98
        height: 98
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 59c68cdb6de853e00800000000000000
      internalID: 1023881543254830229
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: workers_big_priestmaya
      rect:
        serializedVersion: 2
        x: 388
        y: 1029
        width: 98
        height: 110
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: f5c10a5e22d130f80800000000000000
      internalID: -8141631665637155745
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: workers_priestmaya
      rect:
        serializedVersion: 2
        x: 491
        y: 1028
        width: 72
        height: 80
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 28ec978048b92e380800000000000000
      internalID: -7713781604464504857
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: workers_small_priestmaya
      rect:
        serializedVersion: 2
        x: 565
        y: 1028
        width: 61
        height: 66
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 86c7432a1748db5c0800000000000000
      internalID: -4198053654051914648
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: workers_big_city_mayas
      rect:
        serializedVersion: 2
        x: 628
        y: 1028
        width: 104
        height: 101
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: b58cedfc045f952e0800000000000000
      internalID: -2136406889496393637
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: workers_city_mayas
      rect:
        serializedVersion: 2
        x: 736
        y: 1028
        width: 70
        height: 70
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 8056eef49de9f6650800000000000000
      internalID: 6228371465862800648
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: workers_small_city_mayas
      rect:
        serializedVersion: 2
        x: 807
        y: 1028
        width: 54
        height: 53
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 17958e2de0c214d10800000000000000
      internalID: 2108014542765709681
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: workers_big_city_china
      rect:
        serializedVersion: 2
        x: 6
        y: 1131
        width: 109
        height: 107
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: cb15162a508e9e2a0800000000000000
      internalID: -6707575059094875716
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: workers_city_china
      rect:
        serializedVersion: 2
        x: 122
        y: 1130
        width: 74
        height: 73
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 60398fef000333f40800000000000000
      internalID: 5706957933644387078
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: workers_small_city_china
      rect:
        serializedVersion: 2
        x: 200
        y: 1131
        width: 61
        height: 58
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: c7026f61cd9fd32d0800000000000000
      internalID: -3297204628491984772
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: workers_city_egypt
      rect:
        serializedVersion: 2
        x: 865
        y: 1026
        width: 74
        height: 71
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: fa91afbc20e3ce7a0800000000000000
      internalID: -6346629593138849361
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: workers_small_city_egypt
      rect:
        serializedVersion: 2
        x: 942
        y: 1026
        width: 58
        height: 55
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 6e3d6dba636b7f0d0800000000000000
      internalID: -3389039848645536794
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: ebc_madscientistslab
      rect:
        serializedVersion: 2
        x: 780
        y: 118
        width: 64
        height: 94
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 941731a03f9268000800000000000000
      internalID: 37763770702065993
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: ebc_piratefortress
      rect:
        serializedVersion: 2
        x: 263
        y: 1125
        width: 107
        height: 93
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: e56e32ec9f2ce34e0800000000000000
      internalID: -1999946806344817058
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: cta_maya_luxurious_workshop_level2
      rect:
        serializedVersion: 2
        x: 370
        y: 1138
        width: 233
        height: 107
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: c9e7b8d5005e41730800000000000000
      internalID: 3969048961352433308
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: ebc_wintermarket
      rect:
        serializedVersion: 2
        x: 603
        y: 1136
        width: 112
        height: 103
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: f675c9f9fde366e00800000000000000
      internalID: 1037585894331930479
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: info
      rect:
        serializedVersion: 2
        x: 740
        y: 1103
        width: 52
        height: 52
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 555cee1a89f97b910800000000000000
      internalID: 1853125249586873685
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: ebc_trojanhorse
      rect:
        serializedVersion: 2
        x: 811
        y: 1113
        width: 101
        height: 95
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 33886dc4c3b71ae70800000000000000
      internalID: 9124709818946717747
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: icon_chest_good2
      rect:
        serializedVersion: 2
        x: 912
        y: 1109
        width: 90
        height: 92
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 0f532f85e6e454d80800000000000000
      internalID: -8267115305123039760
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: ebc_broch
      rect:
        serializedVersion: 2
        x: 935
        y: 1202
        width: 89
        height: 95
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 2e3bdc3b6e39adc70800000000000000
      internalID: 8996665824694350818
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: cta_manuscript
      rect:
        serializedVersion: 2
        x: 443
        y: 1458
        width: 100
        height: 102
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 682046a9a8bad2040800000000000000
      internalID: 4624541004163449478
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: cta_barrel
      rect:
        serializedVersion: 2
        x: 105
        y: 1203
        width: 82
        height: 103
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 0c31d79c2d5b7cf10800000000000000
      internalID: 2289998852469363648
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: cta_herbs
      rect:
        serializedVersion: 2
        x: 188
        y: 1189
        width: 74
        height: 115
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: e163e1a399cc83a90800000000000000
      internalID: -7333887034695731682
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: cta_icon_chest_blueprint
      rect:
        serializedVersion: 2
        x: 263
        y: 1219
        width: 102
        height: 92
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 1fbbbfc1ed2fb1f00800000000000000
      internalID: 1088730770723027953
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: cta_icon_chest_rp
      rect:
        serializedVersion: 2
        x: 367
        y: 1246
        width: 102
        height: 81
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 727b87a099bb3e4e0800000000000000
      internalID: -1953511547368589529
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: ebc_persianpalace
      rect:
        serializedVersion: 2
        x: 825
        y: 1216
        width: 99
        height: 120
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: b3a071af20ced8440800000000000000
      internalID: 4939863863825205819
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: shoreline
      rect:
        serializedVersion: 2
        x: 3
        y: 1343
        width: 83
        height: 78
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 7f44f5034e26aa0f0800000000000000
      internalID: -1104962026362419977
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: cta_mead
      rect:
        serializedVersion: 2
        x: 90
        y: 1307
        width: 147
        height: 98
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: c530aa5543cc839d0800000000000000
      internalID: -2794259043635821732
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: cta_stockfish
      rect:
        serializedVersion: 2
        x: 238
        y: 1317
        width: 94
        height: 102
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: efb1c1f7f97e89770800000000000000
      internalID: 8617892559191809022
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: cta_spice_treasure
      rect:
        serializedVersion: 2
        x: 333
        y: 1328
        width: 106
        height: 83
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: bfb2e3f561cee1f40800000000000000
      internalID: 5701253759128972283
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: cta_gold_treasure
      rect:
        serializedVersion: 2
        x: 440
        y: 1353
        width: 106
        height: 102
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: fa5149c917f525a70800000000000000
      internalID: 8814212362280375727
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: icon_gem_treasure
      rect:
        serializedVersion: 2
        x: 547
        y: 1354
        width: 101
        height: 72
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: d57f9ba6b633a4bb0800000000000000
      internalID: -4951088303895349411
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: cta_pennies
      rect:
        serializedVersion: 2
        x: 491
        y: 1252
        width: 95
        height: 101
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: fa94068ad7f8b36e0800000000000000
      internalID: -1856732651501631057
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: cta_honey
      rect:
        serializedVersion: 2
        x: 595
        y: 1252
        width: 102
        height: 101
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 4d7729ca4dbb101d0800000000000000
      internalID: -3386218922703161388
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: cta_fish
      rect:
        serializedVersion: 2
        x: 702
        y: 1251
        width: 88
        height: 102
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: f9e4a1c7f68165a80800000000000000
      internalID: -8478562381375844705
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: cta_ceramic_treasure
      rect:
        serializedVersion: 2
        x: 924
        y: 1299
        width: 100
        height: 102
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 22dedf67b0ddfe140800000000000000
      internalID: 4751259173209894178
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: pennies
      rect:
        serializedVersion: 2
        x: 543
        y: 958
        width: 61
        height: 65
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 1fe7a25aa2f3eda60800000000000000
      internalID: 7700661865242328817
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: workers_big_city_vikings
      rect:
        serializedVersion: 2
        x: 8
        y: 1423
        width: 104
        height: 115
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 25a88d2d19d5b0370800000000000000
      internalID: 8289822419994577490
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: workers_city_vikings
      rect:
        serializedVersion: 2
        x: 115
        y: 1422
        width: 71
        height: 80
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: e09adcfe40deed920800000000000000
      internalID: 3017109405843695886
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: workers_small_city_vikings
      rect:
        serializedVersion: 2
        x: 190
        y: 1423
        width: 56
        height: 63
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 405634c405f86f500800000000000000
      internalID: 429688389500101892
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: workers_big_sailorvikings
      rect:
        serializedVersion: 2
        x: 4
        y: 1541
        width: 112
        height: 105
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: cd30dccf6af7e7a60800000000000000
      internalID: 7673711170268496860
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: workers_sailorvikings
      rect:
        serializedVersion: 2
        x: 122
        y: 1505
        width: 74
        height: 72
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: c101932e8d3223560800000000000000
      internalID: 7291930161081552924
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: workers_small_sailorvikings
      rect:
        serializedVersion: 2
        x: 201
        y: 1490
        width: 60
        height: 59
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 9c16a598b7af952f0800000000000000
      internalID: -983479635111419447
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: cta_mixedchest
      rect:
        serializedVersion: 2
        x: 653
        y: 1354
        width: 108
        height: 103
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: f63a9d1b880da02b0800000000000000
      internalID: -5617448299673312401
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: cta_icon_mystery_chest_gold
      rect:
        serializedVersion: 2
        x: 715
        y: 1162
        width: 82
        height: 87
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: ee588dcd66ff95be0800000000000000
      internalID: -1487877384612313618
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: blueprint_rare
      rect:
        serializedVersion: 2
        x: 783
        y: 1354
        width: 125
        height: 114
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 3532b03158787f8e0800000000000000
      internalID: -1659708932042841261
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: InlineIcons_122
      rect:
        serializedVersion: 2
        x: 913
        y: 1402
        width: 98
        height: 94
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 9ea0031e8a76c6b30800000000000000
      internalID: 4281911320752687849
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: wonder_active_checkmark
      rect:
        serializedVersion: 2
        x: 1
        y: 1650
        width: 67
        height: 66
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 8288ff3d8ead33c90800000000000000
      internalID: -7191163486456543192
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: wonder_inactive_checkmark
      rect:
        serializedVersion: 2
        x: 70
        y: 1650
        width: 68
        height: 66
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: db2ba8d5ff6bc6c60800000000000000
      internalID: 7812820661483254461
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: wonder_orb
      rect:
        serializedVersion: 2
        x: 926
        y: 1501
        width: 95
        height: 88
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: e3757416b1d00b7d0800000000000000
      internalID: -2904807348406626498
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: gears
      rect:
        serializedVersion: 2
        x: 804
        y: 1470
        width: 96
        height: 93
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 5e8d2be7664108a60800000000000000
      internalID: 7674156195484195045
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: ebc_madrasa
      rect:
        serializedVersion: 2
        x: 925
        y: 1588
        width: 98
        height: 94
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: e63d19e6edd72ce90800000000000000
      internalID: -7006899675943808146
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: ebc_shrineofreflection
      rect:
        serializedVersion: 2
        x: 1
        y: 1248
        width: 89
        height: 85
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 4a2504689265dcc40800000000000000
      internalID: 5534174253454217892
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: cta_gears
      rect:
        serializedVersion: 2
        x: 727
        y: 1469
        width: 72
        height: 69
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 433a7a9a33adc6040800000000000000
      internalID: 4642325231336661812
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: cta_wax_seal
      rect:
        serializedVersion: 2
        x: 825
        y: 1566
        width: 101
        height: 103
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 3, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 2b3a5bc8438f6de20800000000000000
      internalID: 3375157875343467442
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: cta_door
      rect:
        serializedVersion: 2
        x: 726
        y: 1537
        width: 81
        height: 102
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 3f166c35c999b54f0800000000000000
      internalID: -838908008875007501
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: cta_saffron
      rect:
        serializedVersion: 2
        x: 623
        y: 1458
        width: 103
        height: 96
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 74dc22db63ac63cd0800000000000000
      internalID: -2578651400172810937
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: ebc_cryptofthecount
      rect:
        serializedVersion: 2
        x: 942
        y: 1685
        width: 80
        height: 116
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: f138df648f0c4f830800000000000000
      internalID: 4104117333016806175
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: InlineIcons_130
      rect:
        serializedVersion: 2
        x: 261
        y: 1419
        width: 101
        height: 95
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 9f7965320d0d106c0800000000000000
      internalID: -4178829386858457095
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: cta_building_vikings_sailorport_premium_1
      rect:
        serializedVersion: 2
        x: 822
        y: 1670
        width: 119
        height: 104
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 831ba3e801aa4e320800000000000000
      internalID: 2586379074099917112
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: cta_building_vikings_sailorport_premium_2
      rect:
        serializedVersion: 2
        x: 674
        y: 1657
        width: 147
        height: 103
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 9b2da750d6d53dff0800000000000000
      internalID: -12563651127225671
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: cta_building_vikings_sailorport
      rect:
        serializedVersion: 2
        x: 653
        y: 1554
        width: 72
        height: 103
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: a910c72794f6246f0800000000000000
      internalID: -701876230672350822
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: InlineIcons_133
      rect:
        serializedVersion: 2
        x: 903
        y: 1801
        width: 121
        height: 102
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: ef155f832a0423b20800000000000000
      internalID: 3112621357950915070
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: InlineIcons_134
      rect:
        serializedVersion: 2
        x: 903
        y: 1918
        width: 116
        height: 130
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 8161b42a1621c6bf0800000000000000
      internalID: -329868462160931304
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: InlineIcons_135
      rect:
        serializedVersion: 2
        x: 0
        y: 1717
        width: 138
        height: 139
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 6e1d253a691ab8a80800000000000000
      internalID: -8463493406355435034
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: InlineIcons_136
      rect:
        serializedVersion: 2
        x: 148
        y: 1599
        width: 104
        height: 112
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 0ae6884a4435dec10800000000000000
      internalID: 2084413756825693856
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: flat_cancel
      rect:
        serializedVersion: 2
        x: 4
        y: 1856
        width: 87
        height: 87
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: ad7ae843f77419230800000000000000
      internalID: 3643772185187624922
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: flat_check
      rect:
        serializedVersion: 2
        x: 101
        y: 1856
        width: 114
        height: 87
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 5eb85cf747d3133f0800000000000000
      internalID: -922888878065087515
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: bb_lock_grey
      rect:
        serializedVersion: 2
        x: 466
        y: 807
        width: 38
        height: 50
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 36915f1f000fa9c10800000000000000
      internalID: 2061223666325461347
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: icon_crate
      rect:
        serializedVersion: 2
        x: 138
        y: 1715
        width: 54
        height: 50
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: ddb26354dae87d6d0800000000000000
      internalID: -2965744954758517795
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: icon_star
      rect:
        serializedVersion: 2
        x: 202
        y: 1718
        width: 56
        height: 56
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 33dc314476f52bf20800000000000000
      internalID: 3436914362765528371
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: icon_tome
      rect:
        serializedVersion: 2
        x: 796
        y: 1774
        width: 104
        height: 104
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 305faa5e507320360800000000000000
      internalID: 7134325258175837443
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: icon_wardrobe
      rect:
        serializedVersion: 2
        x: 706
        y: 1774
        width: 86
        height: 104
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 197037273c1c4e5f0800000000000000
      internalID: -728244194544253039
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: icon_chili
      rect:
        serializedVersion: 2
        x: 618
        y: 1774
        width: 84
        height: 104
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 3bd132e6fd66030b0800000000000000
      internalID: -5750983614340588109
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: icon_arabia_worker
      rect:
        serializedVersion: 2
        x: 271
        y: 1571
        width: 81
        height: 79
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 3a68dd74c06e438c0800000000000000
      internalID: -4020335626915314013
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: icon_dirham
      rect:
        serializedVersion: 2
        x: 352
        y: 1572
        width: 60
        height: 71
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 374e92a8ae2e0f430800000000000000
      internalID: 3814798381351036019
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: icon_gold_dinar
      rect:
        serializedVersion: 2
        x: 412
        y: 1572
        width: 64
        height: 71
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: b3989c84048541210800000000000000
      internalID: 1302763225339234619
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: icon_incense
      rect:
        serializedVersion: 2
        x: 476
        y: 1565
        width: 58
        height: 84
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 19810dc017d415a10800000000000000
      internalID: 1896382066041297041
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: icon_coffee_beans
      rect:
        serializedVersion: 2
        x: 277
        y: 1650
        width: 74
        height: 50
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 5e47d1778f49e46a0800000000000000
      internalID: -6463064620358863643
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: icon_myrrh
      rect:
        serializedVersion: 2
        x: 352
        y: 1650
        width: 76
        height: 63
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 7bb572083ea68bd90800000000000000
      internalID: -7081792888699921481
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: icon_brass
      rect:
        serializedVersion: 2
        x: 429
        y: 1650
        width: 74
        height: 66
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: f337afe23ee2f9570800000000000000
      internalID: 8475544577015182143
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: icon_coffee
      rect:
        serializedVersion: 2
        x: 269
        y: 1700
        width: 80
        height: 81
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 1, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 7b6485f191e49ec50800000000000000
      internalID: 6694968190869980855
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: icon_camel
      rect:
        serializedVersion: 2
        x: 349
        y: 1713
        width: 67
        height: 89
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 5f2051f6eb1057440800000000000000
      internalID: 4932850884273308405
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: InlineIcons_154
      rect:
        serializedVersion: 2
        x: 538
        y: 1564
        width: 107
        height: 102
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 4779c75665e3a23b0800000000000000
      internalID: -5536544251107829900
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: cta_icon_mystery_chest
      rect:
        serializedVersion: 2
        x: 532
        y: 1676
        width: 113
        height: 90
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 402283127de034c70800000000000000
      internalID: 8954016801280762372
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: cta_icon_loot_container
      rect:
        serializedVersion: 2
        x: 497
        y: 1771
        width: 120
        height: 98
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 01e40d22912a13a90800000000000000
      internalID: -7335904089167868400
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: cta_icon_loot_container_bronze
      rect:
        serializedVersion: 2
        x: 500
        y: 1869
        width: 116
        height: 95
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 6d1b53fa5527d2dc0800000000000000
      internalID: -3662145209645026858
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: InlineIcons_158
      rect:
        serializedVersion: 2
        x: 761
        y: 1919
        width: 141
        height: 116
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 8ec54f44a8c0b2d10800000000000000
      internalID: 2101787439100353768
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: icon_event_pegasus_tokens
      rect:
        serializedVersion: 2
        x: 651
        y: 1886
        width: 97
        height: 106
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 43fb3241d77dd3d50800000000000000
      internalID: 6718763151315418932
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: icon_event_greek_2023_grand_prize_progress
      rect:
        serializedVersion: 2
        x: 3
        y: 1947
        width: 96
        height: 101
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 642ac1ad98a093170800000000000000
      internalID: 8158563787144471110
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: icon_alliance_point_rare
      rect:
        serializedVersion: 2
        x: 575
        y: 1961
        width: 74
        height: 87
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 573717ceb7fbae349bdebf8d08b3d181
      internalID: -1619160577
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: icon_alliance_point_epic
      rect:
        serializedVersion: 2
        x: 501
        y: 1961
        width: 74
        height: 87
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: da5adb701da504246ae9f7a485068ceb
      internalID: -724805922
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: icon_alliance_point
      rect:
        serializedVersion: 2
        x: 427
        y: 1961
        width: 73
        height: 87
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 7ffcc435ba857794eb9b402c043c281c
      internalID: 2144358214
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: InlineIcons_0
      rect:
        serializedVersion: 2
        x: 101
        y: 1947
        width: 119
        height: 101
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 85509f06080011c46a059d23e4fcd470
      internalID: 370364008
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: InlineIcons_1
      rect:
        serializedVersion: 2
        x: 228
        y: 1789
        width: 95
        height: 123
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 788fc962772e8fa4c83bbec04b8d6218
      internalID: -702928828
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: icon_checkmark_brown
      rect:
        serializedVersion: 2
        x: 850
        y: 957
        width: 54
        height: 44
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: fa55e09056a017a4aac91a8b3f4e28b4
      internalID: -1670182809
      vertices: []
      indices: 
      edges: []
      weights: []
    outline: []
    physicsShape: []
    bones: []
    spriteID: e3ca2b924617e4dd89f48a6ee355034e
    internalID: 0
    vertices: []
    indices: 
    edges: []
    weights: []
    secondaryTextures: []
    nameFileIdTable:
      InlineIcons_0: 370364008
      InlineIcons_1: -702928828
      InlineIcons_122: 4281911320752687849
      InlineIcons_130: -4178829386858457095
      InlineIcons_133: 3112621357950915070
      InlineIcons_134: -329868462160931304
      InlineIcons_135: -8463493406355435034
      InlineIcons_136: 2084413756825693856
      InlineIcons_154: -5536544251107829900
      InlineIcons_158: 2101787439100353768
      InlineIcons_32: 7972151216418658502
      ath_attempt: 7681201850867341242
      bb_checkmark: 21300016
      bb_lock: 7245690677989368111
      bb_lock_grey: 2061223666325461347
      blueprint: 3495086814825567685
      blueprint_rare: -1659708932042841261
      boost: 21300014
      clock_blue: -8246627260571945669
      clock_embossed_pos: -6279640769779937744
      cocoa: 6107784321054212483
      coin_food_boost: 2441754920960949527
      coins: 21300002
      coins_small: -1869673367297931309
      commander: -5437674158304577141
      cta_ancestor_mask: -5159087384432534610
      cta_ankh: -7343220986905845580
      cta_barrel: 2289998852469363648
      cta_building_vikings_sailorport: -701876230672350822
      cta_building_vikings_sailorport_premium_1: 2586379074099917112
      cta_building_vikings_sailorport_premium_2: -12563651127225671
      cta_calendar_stone: 8678780475385641507
      cta_cape: 8287165777748813492
      cta_ceramic_treasure: 4751259173209894178
      cta_ceremonial_dress: -2518806979871309895
      cta_clay: -7411174152488024004
      cta_cocoa: 5803713721798503446
      cta_coins: -5167353112666354359
      cta_column: 8640258737205591229
      cta_door: -838908008875007501
      cta_feather: 8757602080057577540
      cta_fish: -8478562381375844705
      cta_gears: 4642325231336661812
      cta_goblet: 7462434613906369459
      cta_gold_laurel: -7357144505842695810
      cta_gold_ore: 2117336584302238645
      cta_gold_treasure: 8814212362280375727
      cta_golden_sphinx: -2269303870351376580
      cta_happiness_maya_2: 1023881543254830229
      cta_headdress: 7513239266779225853
      cta_herbs: -7333887034695731682
      cta_honey: -3386218922703161388
      cta_icon_chest_blueprint: 1088730770723027953
      cta_icon_chest_rp: -1953511547368589529
      cta_icon_loot_container: -7335904089167868400
      cta_icon_loot_container_bronze: -3662145209645026858
      cta_icon_mystery_chest: 8954016801280762372
      cta_icon_mystery_chest_gold: -1487877384612313618
      cta_ink: -2217656884674354197
      cta_iron_pendant: -8318277596760801014
      cta_jade: 6409830552331150148
      cta_kaolin: 4936506583271141030
      cta_linen_shirt: -4770960272869957185
      cta_manuscript: 4624541004163449478
      cta_marble_bust: -9021633436630479557
      cta_maya_luxurious_workshop_level2: 3969048961352433308
      cta_mead: -2794259043635821732
      cta_mixedchest: -5617448299673312401
      cta_mosaic: -1321098932929957079
      cta_moth_cocoons: -44565893461480510
      cta_obsidian: 4956319499149316874
      cta_obsidiancalendarancestormask: -4501791974573854292
      cta_obsidianjade: -7043394559850540028
      cta_papyrus: 541087786881136906
      cta_papyrus_scroll: -7215298494019262123
      cta_pennies: -1856732651501631057
      cta_pepper: 5488086946749717670
      cta_planks: -1670260634343250872
      cta_porcelain: 6396128496709196802
      cta_priest: 9112743452827066768
      cta_recruit: 6905562749588875414
      cta_research_points: 8674190453677158122
      cta_rice: -2829443050583330228
      cta_ritual_dagger: -5049782452158657568
      cta_saffron: -2578651400172810937
      cta_salt: -8210193684748564459
      cta_silk: 4524241383834298874
      cta_silk_threads: -7851600766591116406
      cta_silver_ring: -6162265055754030298
      cta_spice_treasure: 5701253759128972283
      cta_stockfish: 8617892559191809022
      cta_stone_tablet: 3537169958838929854
      cta_suns_blessing: -6316392241553622110
      cta_toga: -7230867332484785407
      cta_tunic: 210297501773229927
      cta_wax_seal: 3375157875343467442
      cta_wooden_wheel: -8037489388941252367
      cta_wu_zhu: -8264326421717354311
      culture_bonus: 2102306210881839429
      culture_range: 7459902675860876989
      dead: -9160987577138628238
      deben: -7927273033115742964
      ebc_broch: 8996665824694350818
      ebc_cryptofthecount: 4104117333016806175
      ebc_hydra: -4834095905783300709
      ebc_madrasa: -7006899675943808146
      ebc_madscientistslab: 37763770702065993
      ebc_persianpalace: 4939863863825205819
      ebc_piratefortress: -1999946806344817058
      ebc_shrineofreflection: 5534174253454217892
      ebc_trojanhorse: 9124709818946717747
      ebc_wintermarket: 1037585894331930479
      expansion: -6479823341794910233
      flat_cancel: 3643772185187624922
      flat_check: -922888878065087515
      food: 21300004
      food_small: 255080986811049931
      gears: 7674156195484195045
      glass: 21300008
      goods: 21300006
      happiness_0: 2267314249368087281
      happiness_1: -9053465508694820594
      happiness_2: -1134528729738406305
      happiness_3: 7780953735995972703
      icon_alabaster_idol: 8373675954752092734
      icon_alliance_point: 2144358214
      icon_alliance_point_epic: -724805922
      icon_alliance_point_rare: -1619160577
      icon_arabia_worker: -4020335626915314013
      icon_brass: 8475544577015182143
      icon_bronze_bracelet: 7237261532831544450
      icon_camel: 4932850884273308405
      icon_checkmark_brown: -1670182809
      icon_chest_good2: -8267115305123039760
      icon_chili: -5750983614340588109
      icon_coffee: 6694968190869980855
      icon_coffee_beans: -6463064620358863643
      icon_crate: -2965744954758517795
      icon_dirham: 3814798381351036019
      icon_event_greek_2023_grand_prize_progress: 8158563787144471110
      icon_event_pegasus_tokens: 6718763151315418932
      icon_flint: 1438499179017281121
      icon_food_black: 8706619048834826526
      icon_gem_treasure: -4951088303895349411
      icon_gold_dinar: 1302763225339234619
      icon_hide: 5908195551155024309
      icon_incense: 1896382066041297041
      icon_myrrh: -7081792888699921481
      icon_star: 3436914362765528371
      icon_tome: 7134325258175837443
      icon_trade token: -5645743825326872590
      icon_wardrobe: -728244194544253039
      icon_wool: 358799654582701605
      info: 1853125249586873685
      irrigation: -2457531169152919533
      irrigation_0: 6619405697063382928
      irrigation_1: -5368687836300328278
      irrigation_2: 1461871564780703198
      irrigation_bonus: -8078951251822203351
      map: -399840562033327265
      pennies: 7700661865242328817
      premium: 21300000
      ranking: 6195470969790340212
      research_points: 21300012
      research_points_small: 6547572931748238266
      scout: 1357911645972831016
      shoreline: -1104962026362419977
      swords: 1260782343283211980
      trade: -3402344481944718353
      tutorial_icon_build: 4721570850333449918
      tutorial_icon_incident: -2043815690625100865
      tutorial_icon_produce: 1954612522021269676
      unit_cavalry: -497568770147540219
      unit_melee: 2437483765381717123
      unit_range: 5390246818323809478
      upgrade: 7096138885364736195
      upgrade_small: -8988441530965576436
      warning: -1571012651542167539
      wonder_active_checkmark: -7191163486456543192
      wonder_inactive_checkmark: 7812820661483254461
      wonder_orb: -2904807348406626498
      wonders: 5078477160627523986
      workers_big_city_capital: 21300010
      workers_big_city_china: -6707575059094875716
      workers_big_city_egypt: 921062424029211809
      workers_big_city_mayas: -2136406889496393637
      workers_big_city_vikings: 8289822419994577490
      workers_big_priestmaya: -8141631665637155745
      workers_big_sailorvikings: 7673711170268496860
      workers_city_capital: 3121471347900928983
      workers_city_china: 5706957933644387078
      workers_city_egypt: -6346629593138849361
      workers_city_mayas: 6228371465862800648
      workers_city_vikings: 3017109405843695886
      workers_priestmaya: -7713781604464504857
      workers_sailorvikings: 7291930161081552924
      workers_small_city_capital: 4513387777306473356
      workers_small_city_china: -3297204628491984772
      workers_small_city_egypt: -3389039848645536794
      workers_small_city_mayas: 2108014542765709681
      workers_small_city_vikings: 429688389500101892
      workers_small_priestmaya: -4198053654051914648
      workers_small_sailorvikings: -983479635111419447
      workers_trading_big: 1494432333228735887
      wu_zhu: -7907368816393526887
  spritePackingTag: 
  pSDRemoveMatte: 0
  pSDShowRemoveMatteOption: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
