[gd_scene load_steps=4 format=3 uid="uid://by8d3e0svkp5s"]

[ext_resource type="Script" uid="uid://cp2at26p3id78" path="res://player/bullet.gd" id="1_84yui"]
[ext_resource type="PackedScene" uid="uid://njbhsv307xvy" path="res://player/projectile/projectile_3d.tscn" id="2_to65g"]

[sub_resource type="SphereShape3D" id="SphereShape3D_v7oki"]
radius = 0.255727

[node name="Bullet3D" type="Area3D"]
transform = Transform3D(0.14, 0, 0, 0, 0.14, 0, 0, 0, 0.14, 0, 0, 0)
rotation_order = 0
top_level = true
script = ExtResource("1_84yui")

[node name="CollisionShape3D" type="CollisionShape3D" parent="."]
shape = SubResource("SphereShape3D_v7oki")
debug_fill = false

[node name="projectile3D" parent="." instance=ExtResource("2_to65g")]

[connection signal="body_entered" from="." to="." method="_on_body_entered"]
