import _plotly_utils.basevalidators


class ValuesuffixValidator(_plotly_utils.basevalidators.StringValidator):
    def __init__(self, plotly_name="valuesuffix", parent_name="sankey", **kwargs):
        super(ValuesuffixValidator, self).__init__(
            plotly_name=plotly_name,
            parent_name=parent_name,
            edit_type=kwargs.pop("edit_type", "calc"),
            **kwargs,
        )
