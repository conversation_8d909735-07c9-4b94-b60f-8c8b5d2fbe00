import _plotly_utils.basevalidators


class BackoffsrcValidator(_plotly_utils.basevalidators.SrcValidator):
    def __init__(self, plotly_name="backoffsrc", parent_name="scatter.line", **kwargs):
        super(BackoffsrcValidator, self).__init__(
            plotly_name=plotly_name,
            parent_name=parent_name,
            edit_type=kwargs.pop("edit_type", "none"),
            **kwargs,
        )
