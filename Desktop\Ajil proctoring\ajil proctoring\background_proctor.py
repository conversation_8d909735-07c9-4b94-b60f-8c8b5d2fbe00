"""
Background Proctoring Service
Runs invisibly in the background, monitoring students during tests
"""
import cv2
import time
import threading
import logging
import json
from datetime import datetime, timezone
from pathlib import Path
import numpy as np

# Import our database models and utilities
from database_models import (
    log_monitoring_event, record_session_metrics,
    get_session_by_token, ProctorSession, get_db_session
)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('background_proctor.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


class BackgroundProctor:
    """Background proctoring service that monitors students invisibly"""
    
    def __init__(self, session_token):
        self.session_token = session_token
        self.session = None
        self.running = False
        self.camera = None
        self.monitoring_thread = None
        
        # Load face detection model
        try:
            self.face_cascade = cv2.CascadeClassifier(cv2.data.haarcascades + 'haarcascade_frontalface_default.xml')
            logger.info("Face detection model loaded successfully")
        except Exception as e:
            logger.error(f"Failed to load face detection model: {e}")
            self.face_cascade = None
        
        # Monitoring metrics
        self.metrics = {
            'face_detected': False,
            'face_count': 0,
            'face_confidence': 0.0,
            'looking_away': False,
            'attention_score': 0.0,
            'tab_switches': 0,
            'fullscreen_exits': 0
        }
        
        # Event counters
        self.violation_count = 0
        self.last_face_time = time.time()
        self.attention_scores = []
        
    def initialize_session(self):
        """Initialize the proctoring session"""
        try:
            self.session = get_session_by_token(self.session_token)
            if not self.session:
                logger.error(f"Session not found for token: {self.session_token}")
                return False
            
            if self.session.status != 'created':
                logger.error(f"Session {self.session.id} is not in 'created' status")
                return False
            
            # Start the session
            db = get_db_session()
            try:
                self.session.start_session()
                db.merge(self.session)
                db.commit()
                logger.info(f"Session {self.session.id} started successfully")
                return True
            finally:
                db.close()
                
        except Exception as e:
            logger.error(f"Error initializing session: {e}")
            return False
    
    def initialize_camera(self):
        """Initialize camera for monitoring"""
        try:
            self.camera = cv2.VideoCapture(0)
            if not self.camera.isOpened():
                logger.error("Failed to open camera")
                return False
            
            # Set camera properties for better performance
            self.camera.set(cv2.CAP_PROP_FRAME_WIDTH, 640)
            self.camera.set(cv2.CAP_PROP_FRAME_HEIGHT, 480)
            self.camera.set(cv2.CAP_PROP_FPS, 15)
            
            logger.info("Camera initialized successfully")
            return True
            
        except Exception as e:
            logger.error(f"Error initializing camera: {e}")
            return False
    
    def start_monitoring(self):
        """Start background monitoring"""
        if not self.initialize_session():
            return False
        
        if not self.initialize_camera():
            return False
        
        self.running = True
        self.monitoring_thread = threading.Thread(target=self._monitoring_loop, daemon=True)
        self.monitoring_thread.start()
        
        logger.info(f"Background monitoring started for session {self.session.id}")
        return True
    
    def stop_monitoring(self):
        """Stop background monitoring"""
        self.running = False
        
        if self.monitoring_thread:
            self.monitoring_thread.join(timeout=5)
        
        # CRITICAL FIX: Proper camera cleanup to prevent resource conflicts
        if self.camera:
            try:
                self.camera.release()
                self.camera = None
            except Exception as e:
                logger.error(f"Error releasing camera: {e}")
        
        # Complete the session
        if self.session:
            try:
                db = get_db_session()
                try:
                    self.session.complete_session()
                    self.session.total_violations = self.violation_count
                    self.session.attention_score = np.mean(self.attention_scores) if self.attention_scores else 0.0
                    db.merge(self.session)
                    db.commit()
                    logger.info(f"Session {self.session.id} completed")
                finally:
                    db.close()
            except Exception as e:
                logger.error(f"Error completing session: {e}")
        
        logger.info("Background monitoring stopped")
    
    def _monitoring_loop(self):
        """Main monitoring loop that runs in background"""
        last_metrics_time = time.time()
        metrics_interval = 5  # Record metrics every 5 seconds
        
        while self.running:
            try:
                # Capture frame
                ret, frame = self.camera.read()
                if not ret:
                    logger.warning("Failed to capture frame")
                    time.sleep(1)
                    continue
                
                # Process frame for face detection
                self._process_frame(frame)
                
                # Record metrics periodically
                current_time = time.time()
                if current_time - last_metrics_time >= metrics_interval:
                    self._record_metrics()
                    last_metrics_time = current_time
                
                # CRITICAL FIX: Increased delay to prevent system overload
                time.sleep(0.5)  # 500ms delay to prevent CPU overload and conflicts
                
            except Exception as e:
                logger.error(f"Error in monitoring loop: {e}")
                time.sleep(1)
    
    def _process_frame(self, frame):
        """Process frame for face detection and attention monitoring"""
        if self.face_cascade is None:
            return
        
        try:
            # Convert to grayscale for face detection
            gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
            
            # Detect faces
            faces = self.face_cascade.detectMultiScale(
                gray, scaleFactor=1.1, minNeighbors=5, minSize=(30, 30)
            )
            
            # Update metrics
            self.metrics['face_count'] = len(faces)
            self.metrics['face_detected'] = len(faces) > 0
            
            # Check for violations
            if len(faces) == 0:
                # No face detected
                if time.time() - self.last_face_time > 3:  # 3 seconds without face
                    self._log_violation('face_lost', {'duration': time.time() - self.last_face_time})
                self.metrics['looking_away'] = True
                self.metrics['attention_score'] = 0.0
            elif len(faces) > 1:
                # Multiple faces detected
                self._log_violation('multiple_faces', {'face_count': len(faces)})
                self.metrics['attention_score'] = 0.5
            else:
                # Single face detected - good
                self.last_face_time = time.time()
                self.metrics['looking_away'] = False
                self.metrics['attention_score'] = 1.0
                
                # Calculate face confidence (based on face size)
                face = faces[0]
                face_area = face[2] * face[3]
                frame_area = frame.shape[0] * frame.shape[1]
                self.metrics['face_confidence'] = min(face_area / (frame_area * 0.1), 1.0)
            
            # Store attention score for session summary
            self.attention_scores.append(self.metrics['attention_score'])
            if len(self.attention_scores) > 1000:  # Keep last 1000 scores
                self.attention_scores.pop(0)
                
        except Exception as e:
            logger.error(f"Error processing frame: {e}")
    
    def _log_violation(self, violation_type, data=None):
        """Log a monitoring violation"""
        try:
            severity = 'high' if violation_type in ['multiple_faces', 'tab_switch'] else 'medium'
            
            log_monitoring_event(
                session_id=self.session.id,
                event_type=violation_type,
                event_data=data,
                severity=severity
            )
            
            self.violation_count += 1
            logger.warning(f"Violation logged: {violation_type} (Total: {self.violation_count})")
            
        except Exception as e:
            logger.error(f"Error logging violation: {e}")
    
    def _record_metrics(self):
        """Record current metrics to database"""
        try:
            record_session_metrics(
                session_id=self.session.id,
                metrics_data=self.metrics.copy()
            )
            
        except Exception as e:
            logger.error(f"Error recording metrics: {e}")
    
    def log_browser_event(self, event_type, data=None):
        """Log browser-related events (called from external sources)"""
        if not self.running:
            return
        
        try:
            if event_type == 'tab_switch':
                self.metrics['tab_switches'] += 1
                self._log_violation('tab_switch', data)
            elif event_type == 'fullscreen_exit':
                self.metrics['fullscreen_exits'] += 1
                self._log_violation('fullscreen_exit', data)
            
        except Exception as e:
            logger.error(f"Error logging browser event: {e}")


class BackgroundProctorManager:
    """Manager for multiple background proctoring sessions"""
    
    def __init__(self):
        self.active_proctors = {}
        self.lock = threading.Lock()
    
    def start_session(self, session_token):
        """Start a new background proctoring session"""
        with self.lock:
            if session_token in self.active_proctors:
                logger.warning(f"Session {session_token} already active")
                return False
            
            proctor = BackgroundProctor(session_token)
            if proctor.start_monitoring():
                self.active_proctors[session_token] = proctor
                logger.info(f"Started background proctoring for session {session_token}")
                return True
            else:
                logger.error(f"Failed to start background proctoring for session {session_token}")
                return False
    
    def stop_session(self, session_token):
        """Stop a background proctoring session"""
        with self.lock:
            if session_token in self.active_proctors:
                proctor = self.active_proctors[session_token]
                proctor.stop_monitoring()
                del self.active_proctors[session_token]
                logger.info(f"Stopped background proctoring for session {session_token}")
                return True
            else:
                logger.warning(f"Session {session_token} not found in active sessions")
                return False
    
    def log_browser_event(self, session_token, event_type, data=None):
        """Log browser event for a specific session"""
        with self.lock:
            if session_token in self.active_proctors:
                self.active_proctors[session_token].log_browser_event(event_type, data)
                return True
            else:
                logger.warning(f"Session {session_token} not found for browser event")
                return False
    
    def get_active_sessions(self):
        """Get list of active session tokens"""
        with self.lock:
            return list(self.active_proctors.keys())
    
    def stop_all_sessions(self):
        """Stop all active proctoring sessions"""
        with self.lock:
            for session_token in list(self.active_proctors.keys()):
                self.stop_session(session_token)


# Global manager instance
proctor_manager = BackgroundProctorManager()


def start_background_proctoring(session_token):
    """Start background proctoring for a session"""
    return proctor_manager.start_session(session_token)


def stop_background_proctoring(session_token):
    """Stop background proctoring for a session"""
    return proctor_manager.stop_session(session_token)


def log_browser_event(session_token, event_type, data=None):
    """Log browser event for a session"""
    return proctor_manager.log_browser_event(session_token, event_type, data)


if __name__ == "__main__":
    # Test the background proctor
    import sys
    
    if len(sys.argv) != 2:
        print("Usage: python background_proctor.py <session_token>")
        sys.exit(1)
    
    session_token = sys.argv[1]
    
    try:
        if start_background_proctoring(session_token):
            print(f"Background proctoring started for session {session_token}")
            print("Press Ctrl+C to stop...")
            
            while True:
                time.sleep(1)
        else:
            print(f"Failed to start background proctoring for session {session_token}")
            
    except KeyboardInterrupt:
        print("\nStopping background proctoring...")
        stop_background_proctoring(session_token)
        print("Background proctoring stopped.")
