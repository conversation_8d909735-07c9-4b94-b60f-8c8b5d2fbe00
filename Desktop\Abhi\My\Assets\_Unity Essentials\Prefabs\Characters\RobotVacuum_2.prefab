%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!1 &3431511274363594524
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4759274785140621565}
  - component: {fileID: 8784234809327291877}
  - component: {fileID: 3480404334615866391}
  m_Layer: 0
  m_Name: Indicator_Light
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4759274785140621565
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3431511274363594524}
  serializedVersion: 2
  m_LocalRotation: {x: 0.7071066, y: 0.000000029802322, z: -0, w: 0.70710707}
  m_LocalPosition: {x: -0.262, y: 0, z: 0.097}
  m_LocalScale: {x: 0.04, y: 0.005, z: 0.04}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 5031724374817944518}
  m_LocalEulerAnglesHint: {x: 90, y: 0, z: 0}
--- !u!33 &8784234809327291877
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3431511274363594524}
  m_Mesh: {fileID: 10206, guid: 0000000000000000e000000000000000, type: 0}
--- !u!23 &3480404334615866391
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3431511274363594524}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: c2b031a0afa0a413e93d66214b090bda, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!1 &6661464360818668835
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 5935808096725669181}
  - component: {fileID: 836434290850052818}
  - component: {fileID: 825430031008280828}
  m_Layer: 0
  m_Name: RobotVacuum_2
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &5935808096725669181
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6661464360818668835}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: -2.7462664, y: 0.17286152, z: -0.62927365}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 5031724374817944518}
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!54 &836434290850052818
Rigidbody:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6661464360818668835}
  serializedVersion: 4
  m_Mass: 1
  m_Drag: 0
  m_AngularDrag: 0.05
  m_CenterOfMass: {x: 0, y: 0, z: 0}
  m_InertiaTensor: {x: 1, y: 1, z: 1}
  m_InertiaRotation: {x: 0, y: 0, z: 0, w: 1}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ImplicitCom: 1
  m_ImplicitTensor: 1
  m_UseGravity: 1
  m_IsKinematic: 0
  m_Interpolate: 0
  m_Constraints: 0
  m_CollisionDetection: 0
--- !u!65 &825430031008280828
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6661464360818668835}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 0.486565, y: 0.09673464, z: 0.36612964}
  m_Center: {x: -0.000000059604645, y: 0.0459556, z: 0}
--- !u!1001 &4782541147258999853
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 5935808096725669181}
    m_Modifications:
    - target: {fileID: -8679921383154817045, guid: 84e2ff013e5be45679059c8256334982,
        type: 3}
      propertyPath: m_LocalPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: 84e2ff013e5be45679059c8256334982,
        type: 3}
      propertyPath: m_LocalPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: 84e2ff013e5be45679059c8256334982,
        type: 3}
      propertyPath: m_LocalPosition.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: 84e2ff013e5be45679059c8256334982,
        type: 3}
      propertyPath: m_LocalRotation.w
      value: 0.5000002
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: 84e2ff013e5be45679059c8256334982,
        type: 3}
      propertyPath: m_LocalRotation.x
      value: -0.5000002
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: 84e2ff013e5be45679059c8256334982,
        type: 3}
      propertyPath: m_LocalRotation.y
      value: 0.49999988
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: 84e2ff013e5be45679059c8256334982,
        type: 3}
      propertyPath: m_LocalRotation.z
      value: 0.49999976
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: 84e2ff013e5be45679059c8256334982,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: -90
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: 84e2ff013e5be45679059c8256334982,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: 84e2ff013e5be45679059c8256334982,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 90
      objectReference: {fileID: 0}
    - target: {fileID: -7511558181221131132, guid: 84e2ff013e5be45679059c8256334982,
        type: 3}
      propertyPath: m_Materials.Array.data[0]
      value: 
      objectReference: {fileID: 2100000, guid: 34a14842cd93640eab91c23e6d2f70df, type: 2}
    - target: {fileID: 919132149155446097, guid: 84e2ff013e5be45679059c8256334982,
        type: 3}
      propertyPath: m_Name
      value: RobotVaccuum
      objectReference: {fileID: 0}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects:
    - targetCorrespondingSourceObject: {fileID: -8679921383154817045, guid: 84e2ff013e5be45679059c8256334982,
        type: 3}
      insertIndex: -1
      addedObject: {fileID: 4759274785140621565}
    m_AddedComponents:
    - targetCorrespondingSourceObject: {fileID: 919132149155446097, guid: 84e2ff013e5be45679059c8256334982,
        type: 3}
      insertIndex: -1
      addedObject: {fileID: 5070355599431184999}
  m_SourcePrefab: {fileID: 100100000, guid: 84e2ff013e5be45679059c8256334982, type: 3}
--- !u!4 &5031724374817944518 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: -8679921383154817045, guid: 84e2ff013e5be45679059c8256334982,
    type: 3}
  m_PrefabInstance: {fileID: 4782541147258999853}
  m_PrefabAsset: {fileID: 0}
--- !u!1 &5665411802226420092 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: 919132149155446097, guid: 84e2ff013e5be45679059c8256334982,
    type: 3}
  m_PrefabInstance: {fileID: 4782541147258999853}
  m_PrefabAsset: {fileID: 0}
--- !u!64 &5070355599431184999
MeshCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5665411802226420092}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 5
  m_Convex: 1
  m_CookingOptions: 30
  m_Mesh: {fileID: -2908661488186244680, guid: 84e2ff013e5be45679059c8256334982, type: 3}
