[gd_scene load_steps=3 format=3 uid="uid://c7bb2omma0o0x"]

[ext_resource type="Script" uid="uid://qlqxmk3sk28" path="res://mob/spawner/mob_spawner_3d.gd" id="1_ewxdg"]
[ext_resource type="PackedScene" uid="uid://ca08w82uxn175" path="res://mob/spawner/spawner_model.glb" id="2_y27t3"]

[node name="MobSpawner3D" type="Node3D"]
script = ExtResource("1_ewxdg")

[node name="spawner_model" parent="." instance=ExtResource("2_y27t3")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 4, 0)

[node name="Marker3D" type="Marker3D" parent="."]
unique_name_in_owner = true
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 2, 0)

[node name="Timer" type="Timer" parent="."]
unique_name_in_owner = true
wait_time = 2.0
autostart = true

[connection signal="timeout" from="Timer" to="." method="_on_timer_timeout"]
